import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';

import { MainComponent } from './main/main.component';
import { AuthComponent } from './auth/auth.component';
import { HorizontalLayoutComponent } from './horizontal-layout/horizontal-layout.component';
import { UserComponent } from './users/user.component';
import { VideoConfComponent } from './videoconf/videoconf.component';
import { VideoStreamComponent } from './videostream/videostream.component';
import { LiveTourComponent } from './livetour/livetour.component';
import { HomeComponent } from './home/<USER>';
import { PaymentComponent } from './payment/payment.component';
import { ProductDetailsComponent } from './product-details/product-details.component';
import { CartComponent } from './cart/cart.component';
import { OrderGridComponent } from './order-grid/order-grid.component';
import { WishlistComponent } from './wishlist/wishlist.component';

import { SizeRatioDialogComponent } from './gallery/size-ratio/size-ratio-dialog.component';
import { OrderSummaryComponent } from './order-summary/order-summary.component';

export const AppRoutes: Routes = [
   {
      path: '',
      redirectTo: 'session',
      pathMatch: 'full',
   },
   {
      path: 'session',
      loadChildren: () => import('./session/session.module').then(m => m.SessionDemoModule)
   },

   {
      path: 'h',
      canActivate: [AuthGuard],
      component: HorizontalLayoutComponent,
      children: [{
         path: 'home',
         component: HomeComponent,
         data: { breadcrumb: 'Home' }
      }, {
         path: 'payment',
         component: PaymentComponent,
         data: { breadcrumb: 'Payment' }
      }, {
         path: 'cart',
         component: CartComponent,
         data: { breadcrumb: 'Cart' }
      }, {
         path: 'wishlistGrid',
         component: WishlistComponent,
         data: { breadcrumb: 'Wishlist' }
      }, {
         path: 'orderGrid',
         component: OrderGridComponent,
         data: { breadcrumb: 'Order Grid' }
      }, {
         path: 'orderSummary/:orderUid',
         component: OrderSummaryComponent,
         data: { breadcrumb: 'Order Summary' }
      }, {
         path: 'sizeRatio',
         component: SizeRatioDialogComponent,
         data: { breadcrumb: 'Size Ratio' }
      }, {
         path: 'videoconf',
         component: VideoConfComponent,
         data: { breadcrumb: 'VideoConf' }
      }, {
         path: 'videostream',
         component: VideoStreamComponent,
         data: { breadcrumb: 'Videostream' }
      }, {
         path: 'gallerylist/:cat/:subcat',
         loadChildren: () => import('./gallery/gallery.module').then(m => m.GalleryListManagementModule),
         data: { breadcrumb: 'Gallerylist' }
      }, {
         path: 'prodDet/:id',
         component: ProductDetailsComponent,
         data: { breadcrumb: 'Product details' }
      }],

   }, {
      path: '**',
      redirectTo: 'session'
   }
];

@NgModule({
   imports: [RouterModule.forRoot(AppRoutes, {
      onSameUrlNavigation: 'reload'
   })],
   exports: [RouterModule],
   providers: []
})
export class RoutingModule { }

