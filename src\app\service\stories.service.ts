import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { story } from 'app/model/story.model';
import { User } from 'app/model/user.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StoriesService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  getAllStories(): Observable<story[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<story[]>( Appconfig.BASE_URL+'api/techpack/story/all', {
        headers:headers
      });
}

createStory(stiry: story): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');
  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.post<Response>(Appconfig.BASE_URL + 'api/techpack/story/', story, {
    headers: headers
  });
}

updateStory(story: story): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/story/', story, {
    headers: headers
  });
}

deactivateStory(storyUid: string): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/story/' + storyUid, {
    headers: headers
  });
}

activateStory(story: story): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
  .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/trim/'+ story.uuid +'/activate', story, {
    headers: headers
  });
}

}
