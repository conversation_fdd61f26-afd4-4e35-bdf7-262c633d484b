import { Component, OnInit } from '@angular/core';
import { OrderService } from 'app/service/order.service';
import { CatalogService } from 'app/service/catalog.service';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { ActivatedRoute, Router } from '@angular/router';
import { OrderDetails } from 'app/model/OrderDetails.model';
import { Order } from 'app/model/order.model';
import { CustomerProfile } from 'app/model/customerprofile.model';
import { User } from 'app/model/user.model';
import { ToastrService, ToastrConfig } from 'ngx-toastr';

import { MatDialog, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SizeQuantitySharedComponent } from '../size-quantity-shared/size-quantity-shared.component'


@Component({
  selector: 'wishlist',
  templateUrl: './wishlist.component.html',
  styleUrls: ['./wishlist.component.scss']
})
export class WishlistComponent implements OnInit {
  wishlist: any[];
  tenantConfig: TenantConfig;
  customers: CustomerProfile[] = [];
  user: any;

  constructor(private orderService: OrderService,
    private router: Router,
    private catalogService: CatalogService,
    public dialog: MatDialog,
    private toastrService: ToastrService,) {
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    this.customers = JSON.parse(localStorage.getItem("customers"));
    this.user = JSON.parse(localStorage.getItem("userProfile"));
  }

  ngOnInit(): void {


    this.getWishList();

  }
  public getWishList(): void {
    this.orderService.getWishListItems().subscribe(res => {
      this.wishlist = res;
      console.log("wishlist:", this.wishlist);
      this.loading = false;
    })
  }

  getImg(item): string {
    if (item.imageUid == null || item.imageUid == undefined) {
      return this.tenantConfig.imageBaseUrl + item.catalogUid + ".jpg"
    }
    var idx = (item.imageUid + ",").indexOf(",")
    var img = item.imageUid.substring(0, idx)
    return this.tenantConfig.imageBaseUrl + img
  }



  public addToCart(item): void {
    let addCartItems = [];
    addCartItems.push(item);
    var det = new OrderDetails();
    var ord = new Order();
    var cust = this.customers[0]
    ord.customerUid = this.user.customerUid;
    ord.parentCustomerUid = cust.parentCustomerUid;
    det.order = ord;
    det.items = addCartItems;


    this.catalogService.getByUid(item.catalogUid).subscribe(prod => {
      let dialogRef = this.dialog.open(SizeQuantitySharedComponent, {
        width: '800px',
        data: {

        }
      });
      dialogRef.componentInstance.product = prod
      dialogRef.componentInstance.isDialog = true
      dialogRef.componentInstance.sizeQuantityData.subscribe((data) => {

        var det = new OrderDetails();
        var ord = new Order();
        var cust = this.customers[0]
        ord.customerUid = this.user.customerUid;
        ord.parentCustomerUid = cust.parentCustomerUid;
        det.order = ord;
        det.items = data.items;
        this.orderService.saveOrUpdateOrder(det).subscribe(resp => {
          this.removeWishlist(item);
          this.toastrService.success('', 'Added to cart');
        });

      });
    });

  }

  public addToCartAll(): void {
    let addCartItems = [];
    addCartItems = this.wishlist;
    var det = new OrderDetails();
    var ord = new Order();
    var cust = this.customers[0]
    ord.customerUid = this.user.customerUid;
    ord.parentCustomerUid = cust.parentCustomerUid;
    det.order = ord;
    det.items = addCartItems;
    this.orderService.saveOrUpdateOrder(det).subscribe(data => {
      this.toastrService.success('', 'All items added to cart');
      // this.removeAllWishlist();
    });
  }

  loading: boolean = false;
  public removeWishlist(item): void {
    this.loading = true;
    let wishList: any[] = [];
    let data = item;
    data['opMode'] = 2;
    wishList.push(data);
    this.orderService.saveWishListItem(wishList).subscribe(res => {
      this.getWishList();
      this.toastrService.success('', 'Removed from wishlist');
    });
  }

  removeAllWishlist(): void {
    let removewishlist = this.wishlist.map(item => {
      item['opMode'] = 2;
      return item;
    });
    this.orderService.saveWishListItem(removewishlist).subscribe(res => {
      this.getWishList();
      this.toastrService.success('', 'All removed from wishlist');
    });
  }


  public viewOrderDetailsFromCart(item): void {
    this.router.navigate(['/h/prodDet/' + item.catalogUid]);
  }


}
