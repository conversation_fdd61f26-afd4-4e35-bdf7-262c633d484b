import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { Response } from 'app/model/response.model';
import { trim } from 'app/model/trim.model';
import { User } from 'app/model/User.model';
import { Observable } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class TrimsService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }

  getAllTrims(): Observable<trim[]>{
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({'Content-Type': 'application/json'})
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<trim[]>( Appconfig.BASE_URL+'api/techpack/trim/all', {
        headers:headers
      });
  }


  createTrim(trim: trim): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Response>(Appconfig.BASE_URL + 'api/techpack/trim/', trim, {
      headers: headers
    });
  }

  updateTrim(trim: trim): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/trim/', trim, {
      headers: headers
    });
  }

  deactivateTrim(trimUid: string): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/trim/' + trimUid, {
      headers: headers
    });
  }

  activateTrim(trim: trim): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/trim/'+ trim.uuid +'/activate', trim, {
      headers: headers
    });
  }


}
