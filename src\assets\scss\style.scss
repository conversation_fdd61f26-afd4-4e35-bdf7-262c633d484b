//Table of Content

/*--------------
1.0 Bootstrap
----------------*/
/*--------------
2.0 Mixin
----------------*/
/*--------------
3.0 Core
   3.1 Main Content
   3.2 Scaffolding
   3.3 Button
   3.4 Form Elements
   3.5 Progress Bar
   3.6 Tabs
   3.7 Accordions
   3.8 Pagination
   3.9 Cards
   3.10 Typography
   3.11 Badge
   3.12 Dropdowns
   3.13 Social
   3.14 Table
   3.15 Ribbon
   3.16 Header
   3.17 Sidebar
   3.18 Preloader
   3.19 Dash-section
   3.20 Timeline
   3.21 Dark
   3.22 Rtl
----------------*/
/*--------------
4.0 Elements
   4.1 Effect
   4.2 Slick
   4.3 Session
   4.4 User
   4.5 Pricing
   4.6 Blog
   4.7 Gallery
   4.8 Ecommerce
   4.9 Icons
   4.10 Datepicker
   4.11 Calendar
   4.12 Grid
   4.13 Testimonial
   4.14 Inbox
   4.15 Editor
   4.16 Sidebar-option
   4.17 Animate
   4.18 Tour
   4.19 Courses
   4.20 Video-player
   4.21 Pages
----------------*/
/*--------------  
5.0 Utilities
   5.1 Bg
   5.2 Base
   5.3 Font Text
----------------*/

@import "bootstrap";
@import "mixins";
@import "core";
@import "elements";
@import "utilities/utilities";

.afu-dragndrop-box {
  width: 100% !important;
}

.md-autocomplete-suggestions-container {
  z-index: 100000 !important; /* any number of choice > 1050*/
  pointer-events: all !important;
}

/* /deep/ .cdk-overlay-container { z-index: 9999; } */

.cdk-overlay-container {
  z-index: 9999;
}

.global-loader {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #000;
  z-index: 999999999999;
  opacity: 0.7;
}
.innerSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  height: 150px;
  background: transparent;
  border-radius: 50%;
  text-align: center;
  line-height: 150px;
  font-family: sans-serif;

  font-size: 20px;

  color: blue;

  text-transform: uppercase;

  text-shadow: 0 0 10px blue;

  box-shadow: 0 0 20px #000000;
}

.innerSpinner:before {
  content: "";
  position: absolute;

  top: -3px;
  left: -3px;
  width: 100%;
  height: 100%;

  border: 3px solid transparent;

  border-top: 3px solid blue;
  border-right: 3px solid blue;

  border-radius: 50%;

  animation: animateCircle 2s linear infinite;
}

.innerSpinner1 {
  display: block;
  position: absolute;

  top: calc(50% - 2px);
  left: 50%;
  width: 50%;
  height: 4px;
  background: transparent;
  transform-origin: left;
  animation: animateCircle1 2s linear infinite;
}

.innerSpinner1:before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3f51b5;

  top: -6px;
  right: -8px;
  box-shadow: 0 0 20px blue;
}

@keyframes animateCircle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes animateCircle1 {
  0% {
    transform: rotate(45deg);
  }
  100% {
    transform: rotate(405deg);
  }
}


