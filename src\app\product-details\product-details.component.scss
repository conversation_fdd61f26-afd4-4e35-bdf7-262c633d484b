.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: #414658;
    background-color: #fafbfe;
    border-color: #dee2e6 #dee2e6 #fafbfe;
}
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}
.colourstxt{
    font-size: 14px;
    line-height: 20px;
    font-family: Arial,sans-serif;
    margin-left: 5px;
}
.colours{
    font-size: 16px;
    line-height: 20px;
    font-family: Arial,sans-serif;
    font-weight: 700;
    margin-left: 18px;
}

img {
    margin: 5px;
}
.img-fluid{
  height: 50px;
  width: 38px;
  border:1px solid #e0e0e0;
}

.product-details-wrapper {
    width: 100%;
    height: 100%;
    margin-bottom: 170px;
    margin-top: 20px;
    .width-58 {
        width: 56%;
        padding: 20px;
    }
    .width-40 {
        width: 38%;
        padding: 20px;
    }
    .product-title {
        color: #282c3f;
        font-size: 24px;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        // line-height: 1;
    }

    .style-name {
        color: #535665;
        padding: 5px 20px 14px 0;
        font-size: 20px;
        opacity: 0.8;
        font-weight: 400;
        text-transform: capitalize;
    }
    .margin-right-20 {
        margin-right: 20px;
    }
    .margin-top-20 {
        margin-top: 20px;
    }
    .size-button {
        font-size: 16px;
        font-weight: bold;
    }
    .size-button-chart {
        font-size: 16px;
        font-weight: bold;
    }
    .cursor-pointer {
        cursor: pointer;
    }
    .size-buttons {
        background-color: white;
        color: black;
        border: 1px solid lightgrey;
    }
    .size-buttons:hover {
        border: 1px solid red;
    }
    .margin-top-10 {
        margin-top: 10px;
        margin-right: 15%;
    }
    .spec-border-bottom {
        border-bottom: 1px solid lightgrey;
        padding: 5px;
        .spec-label {
            font-weight: normal;
        }
        .spec-value {
            font-weight: bold;
            font-size: 14px;
        }
    }
}

#sticky {
    position: sticky;
    position: -webkit-sticky;
    height: 1130px;
    top: 50px;
}

.image-count {
    color: red;
}

.product-price {
    font-size: 24px;
    font-weight: bold;
}
.buttons-wrapper{
    display: flex;
    flex-direction: row;
    justify-content: space-between !important;
    align-items: center;
    position: absolute;
    top: 0%;
}
.previous-button{
    margin-left: 10px;
    cursor: pointer;
}
.next-button{
    margin-right: 10px;
    cursor: pointer;
}
.arrow-size{
    font-size: 60px;
}

@media screen and (max-width:600px){
    .row{
        flex-direction: column !important;
    }
    .product-details-wrapper{
        margin-bottom: 20px !important;
    }
}
.plr-40{
    padding:0px 40px !important;
}
.next-prev-buttons{
    display: flex;
    flex-direction: row;
}
.tabPanelWrapper{
    display: flex;
    flex-wrap: wrap;
    width: 100%;
}

::ng-deep .mat-tab-label-active{
    color:orange !important;
}
#product-details-wrapper-id ::ng-deep .mat-tab-label {
    height: 30px;
    padding: 0 1px;
}

.specifications-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-auto-rows: auto;
    gap: 10px; 
  }
  
  .spec-item:nth-child(6) {
    grid-column: 1 / 6; 
  }
  
  .spec-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-bottom: 1px solid #ccc; 
    padding-bottom: 5px;
  }
  
  .spec-label {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
  
  .spec-value {
    font-size: 14px;
    color: #555;
  }
  