import { Component, OnInit , ViewChild, ElementRef } from '@angular/core';
import { Drop } from 'app/model/Drop.model';
import { User } from 'app/model/user.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { DropsService } from 'app/service/drops.service';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { HttpErrorResponse } from '@angular/common/http';

declare const Twitch: any;

@Component({
  selector: 'app-videostream',
  templateUrl: './videostream.component.html',
  styleUrls: ['./videostream.component.scss']
})
export class VideoStreamComponent implements OnInit {

  delivery: Drop;
  rows: Drop[] = [];
  bacRows: Drop[] = [];
  user: User;
  tenantConfig: TenantConfig;

  @ViewChild('videoConfFrame') videoConfFrame: ElementRef


  modalRef: BsModalRef;
  config = {
    animated: true,
    keyboard: true,
    backdrop: true,
    ignoreBackdropClick: false
  };

  constructor(private deliveryService: DropsService, private modalService: BsModalService, private toastr: ToastrService) { }

  ngOnInit() {

    this.user = JSON.parse(localStorage.getItem("userProfile"));

    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));

    var options = {
        width: '100%',
        height: 600,
        channel: "ramakrishna99",
        // video: "<video ID>",
        // collection: "<collection ID>",
      };
      var player = new Twitch.Player("videoConfFrame", options);
      player.setVolume(0.5);

    
  }

  ngAfterViewInit() {
     // this.videoConfFrame.nativeElement.setAttribute('src', this.tenantConfig.videoStreamUrl);
  }  

  getAllDeliveries() {
    this.deliveryService.getAllDeliveries().subscribe(data => {
      this.rows = data;
      this.bacRows = [...data];
    });
  }

  updateFilter(event) {
    const val = event.target.value.toLowerCase();
    if(val == ''){
      this.rows = this.bacRows;
      return;
    }
    const temp = this.bacRows.filter(function(d) {
      return (d.name==null?"":d.name).toLowerCase().indexOf(val) !== -1 || (d.code==null?"":d.code).toLowerCase().indexOf(val) !== -1 || !val;
    });

    // update the rows
    this.rows = temp;
  }


  open(content) {
    this.delivery = new Drop;
    this.modalRef = this.modalService.show(content, Object.assign({}, this.config, { class: 'modal-lg' }));
  }

  saveDelivery() {
    this.deliveryService.createDrop(this.delivery).subscribe(data => {
      this.delivery = new Drop;
      this.getAllDeliveries();
      this.modalRef.hide();
      this.toastr.success("Drop Created");
    }, (err: HttpErrorResponse) => {
      this.toastr.error(err.error.message);
    });
  }

  updateDelivery() {
    this.deliveryService.updateDrop(this.delivery).subscribe(data => {
      this.delivery = new Drop;
      this.getAllDeliveries();
      this.modalRef.hide();
      this.toastr.success("Drop Updated");
    }, (err: HttpErrorResponse) => {
      this.toastr.error(err.error.message);
    });
  }

  edit(row, content): void {
    this.delivery = row;
    this.modalRef = this.modalService.show(content, Object.assign({}, this.config, { class: 'modal-lg' }));
  }

  activate(row) {
    this.deliveryService.activateDrop(row).subscribe(data => {
      this.toastr.success("Activated");
      this.getAllDeliveries();
    }, (err: HttpErrorResponse) => {
      this.toastr.error(err.error.message);
    });
  }

  deactivate(row) {
    this.deliveryService.deactivateDrop(row.uuid).subscribe(data => {
      this.toastr.success("Deactivated");
      this.getAllDeliveries();
    }, (err: HttpErrorResponse) => {
      this.toastr.error(err.error.message);
    });
  }

}
