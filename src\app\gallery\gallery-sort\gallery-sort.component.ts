import { Component, OnInit, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'gallery-sort',
  templateUrl: './gallery-sort.component.html',
  styleUrls: ['./gallery-sort.component.scss']
})
export class GallerySortComponent implements OnInit {

  @Output('change') change = new EventEmitter();

  sortByItems = ['Whats New', 'Popularity', 'Price Low to High', 'Price High to Low'];

  constructor() { }

  ngOnInit(): void {
  }

  sortChanged(event): void {
    this.change.emit(event);
  }

}
