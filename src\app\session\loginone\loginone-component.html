<div class="container">

  <style>
    .login-row {
      margin-top: 10%;
      vertical-align: middle;
    }

    .login-row .login-logo {
      border-right: 2px solid #231f20;
    }

    .login-row .login-logo img {
      max-width: 300px;
      margin-top: 18%;
    }

    .form-signin input[type="text"],
    .form-signin input[type="email"],
    .form-signin input[type="password"] {
      margin-bottom: 10px !important;
      border-radius: 2px !important;
      border-color: #d9dfe8;
    }

    .form-signin h1 {
      color: #484444;
      font-size: 20px;
      margin-bottom: 20px;
    }

    .form-signin h1 small {
      margin-top: 10px;
    }

    .form-signin .form-label {
      font-size: 12px;
      font-weight: 600;
      margin: 0;
      text-transform: uppercase;
      color: #414658;
    }

    .form-signin .btn-primary {
      background-color: #ef3c69 !important;
      border-radius: 20px;
      border-color: #ef3c66 !important;
      min-width: 150px;
      text-transform: uppercase;
    }
  </style>


  <div class="login-page-wrapper">
    <div *ngIf="_showSignInForm" class="row">
      <div class="login-inner-wrapper">
        <div class="row">
          <div class="col-sm-12 col-md-12" style="text-align: center;">
            <img class="img-responsive" [src]="tenantLogo" alt="" style="width: 300px;height: 150px;">
          </div>
        </div>
        <div class="row">
          <form class="form-signin col-sm-12 col-md-12" #form="ngForm" method="post">
            <div class="row">
              <div class="col-sm-12 col-md-12 text-center">
                <h1>Sign in to Roadshow <small class="d-block">Enter your details below</small></h1>
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-12">
              <label class="form-label ml-4">Email Address/Sign-in id</label>
              <div class="col-xl-12 col-lg-12 col-md-12 col-12">
                <input type="text" class="form-control" placeholder="Email Address or Sign-in Id"
                  (keyUp)="resetInvalid()" [(ngModel)]="email" name="email" #userName="ngModel" required autofocus>
              </div>


              <label class="form-label ml-4">Password</label>
              <div class="col-xl-12 col-lg-12 col-md-12 col-12">
                <input type="password" class="form-control" name="password" [(ngModel)]="password"
                  placeholder=" Enter your password" required>
              </div>
              <div class="text-center">
                <div *ngIf="isInvalidCreds" class="text-danger ng-tns-c14-43">{{credsErrorMessage}}
                </div>
                <button class="btn btn-lg btn-primary signin" (click)='logIn("roadshow")' style="margin-bottom: 5px;">
                  Sign In</button>
                
                <!--                                 <button class="btn btn-lg btn-primary signin" (click)='logIn("roadshow")'
                                    style="margin-bottom: 5px;">
                                    Sign in to Road show</button>
                                <button class="btn btn-lg btn-primary signin"
                                    (click)='logIn("readystock")'>
                                    Sign in to Ready Stock</button> -->
              </div>
             <div class="text-center"><a href="https://roadshow.page.link/turtle" alt="Open App">Open App</a></div> 
              <!-- <a (click)="needHelp(supportNumTemplate)" class="pull-right need-help" id="needHelp">Need help? </a><span class="clearfix"></span> -->
            </div>

          </form>


        </div>
        <div class="text-center">
          <!-- <button  style="width: 50%;" type="button"   class="btn btn-primary" (click)="getMobileNumberScreen()" >Sign-in
            with OTP</button> -->
        </div>
      </div>
    </div>


    <div *ngIf="_showMobileNumForm" class="row">
      <div class="login-inner-wrapper">
        <div class="row">
          <div class="col-sm-12 col-md-12" style="text-align: center;">
            <img class="img-responsive" src="assets/img/logo-signin.jpg" alt="" style="width: 300px;">
          </div>
        </div>
        <div class="row">
          <form class="form-signin col-sm-12 col-md-12" [formGroup]="mobForm" method="post">
            <div class="row">
              <div class="col-sm-12 col-md-12 text-center">
                <h1>Sign in to Roadshow <small class="d-block">Enter your details below</small></h1>
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-12">
              <label class="form-label ml-4">mobile phone number</label>
              <div class="col-xl-12 col-lg-12 col-md-12 col-12">
                <input type="text" class="form-control" placeholder="Enter mobile number" formControlName="mobilenumber"
                  name="mobilenumber" required autofocus>
              </div>
              <div>
                <!-- <a class="" (click)="showSignin();">
                                    Click here to Sign-in
                                </a> -->
                <button type="button" class="btn btn-link bckbtn" (click)="showSignin();"> Click here to
                  Sign-in</button>
              </div>

              <div class="text-center">
                <button class="btn btn-lg btn-primary" type="button" (click)="genereateOTP(mobForm)">
                  Generate OTP</button>
              </div>
              <!-- <a href="" class="pull-right need-help">Need help? </a><span class="clearfix"></span> -->
            </div>

          </form>
        </div>
      </div>
    </div>

   

    <div *ngIf="_showOtpForm" class="row">
      <div class="login-inner-wrapper">
        <div class="row">
          <div class="col-sm-12 col-md-12" style="text-align: center;">
            <img class="img-responsive" src="assets/img/logo-signin.jpg" alt="" style="width: 300px;">
          </div>
        </div>
        <div class="row">
          <form class="form-signin col-sm-12 col-md-12" [formGroup]="otpForm" (ngSubmit)=logInwithOtp(otpForm)
            method="post">
            <div class="row">
              <div class="col-sm-12 col-md-12 text-center">
                <h1>Authentication required<small class="d-block">For your security,
                    we need to authenticate your request. We've sent an OTP to the mobile number
                    +91xxxxxxxxxx.
                    Please enter it below to complete verification.</small></h1>
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-12">
              <label class="form-label ml-4">Enter OTP</label>
              <div class="col-xl-12 col-lg-12 col-md-12 col-12">
                <input type="text" class="form-control" placeholder="Enter OTP" formControlName="otpnumber"
                  name="otpnumber" required autofocus>
              </div>

              <div>
                <button type="button" class="btn btn-link bckbtn" (click)="showSignin();">Click here to
                  Sign-in page</button>
              </div>

              <div class="text-center">
                <button class="btn btn-lg btn-primary" type="submit">
                  Submit</button>
              </div>
              <!-- <a href="" class="pull-right need-help">Need help? </a><span class="clearfix"></span> -->
            </div>

          </form>
        </div>
      </div>
    </div>

  </div>


  <!-- <div class="row login-row">
        <div class="col-sm-6 col-md-4 col-md-offset-2 login-logo">
            <img class="img-responsive" src="https://payload.cargocollective.com/1/3/109301/7601297/FashionRepublica_logo_online_s_650.jpg" alt="">
        </div>
        <div class="col-sm-6 col-md-4">
            <form class="form-signin" #form="ngForm" (ngSubmit)=logIn(form.value)  method="post">
                <h1>Sign in to Roadshow <small class="d-block">Enter your details below</small></h1>
                <label class="form-label">Email Address/Sign-in id</label>
                <input type="text" class="form-control" placeholder="Email Address or Sign-in Id" (keyUp)="resetInvalid()" [(ngModel)]="email" name="email"  #userName="ngModel" required autofocus>

                <label class="form-label">Password</label>
                <input type="password" class="form-control"  name="password" [(ngModel)]="password" placeholder=" Enter your password" required>
                <div class="text-center">
                <small *ngIf="isInvalidCreds" class="text-danger ng-tns-c14-43">Invalid email/password.</small>
                <button class="btn btn-lg btn-primary" type="submit">
                    Sign in</button>
                </div>
               label class="checkbox pull-left ml-3">
                    <input type="checkbox" value="remember-me">
                    Remember me
                </label
                <a href="" class="pull-right need-help">Need help? </a><span class="clearfix"></span>
            </form>
        </div>
    </div> -->

  <!-- div class="row2">
        <div class="col-sm-6 col-md-4 col-md-offset-4">
            <h1 style="font-size:20px;">Sign in to continue to CoCreate </h1>
            <div class="account-wall">
                <img class="profile-img" src="https://lh5.googleusercontent.com/-b0-k99FZlyE/AAAAAAAAAAI/AAAAAAAAAAA/eu7opA4byxI/photo.jpg?sz=120" alt="">
                <form class="form-signin" #signinForm="ngForm" (ngSubmit)="onSubmit()" method="post">
                <input type="text" class="form-control" placeholder="Email Address" name="email" [(ngModel)]="user.userName" #userName="ngModel" required autofocus>
                <input type="password" class="form-control"  placeholder="Password" name="password" [(ngModel)]="user.password"required>
                <button class="btn btn-lg btn-primary btn-block" (click)="login()" type="submit">
                    Sign in</button>
                <label class="checkbox pull-left">
                    <input type="checkbox" value="remember-me">
                    Remember me
                </label>
                <a href="#" class="pull-right need-help">Need help? </a><span class="clearfix"></span>
                </form>
            </div>
        </div>
    </div -->


  <!-- </div> -->


  <!--div class="loginone-wrapper">

    <div class="login-content text-center">
        <div class="login-inner-wrapper-2">
            <div class="mb-2">
                <span class="square-100"><i class="fa fa-unlock-alt text-warning"></i></span>
            </div>
            <div class="mb-3">
                <h2 class="text-muted">Sign into CoCreate</h2>
            </div>
            <form #signinForm="ngForm" (ngSubmit)="onSubmit()" autocomplete="off" class="login-form"  method="post">
                <div class="form-group">
                    <input type="email" name="email" class="form-control form-control-lg input-square" placeholder="Email Address" [(ngModel)]="user.userName" required #userName="ngModel">
                </div>
                <div [hidden]="userName.valid || userName.pristine"
                class="alert alert-danger">
                email is required
            </div>
            <div class="form-group">
                <input name="Password" class="form-control form-control-lg input-square" type="password" placeholder="Password" [(ngModel)]="user.password" required>
            </div>
            <div class="login-meta mb-3">
                <div class="row">
                    <div class="col-xs-6 col-sm-6 col-md-6">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 d-flex">
                            <input class="custom-control-input" type="checkbox">
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description">Remember Me</span>
                        </label>
                    </div>
                    <div class="col-xs-6 col-sm-6 col-md-6 text-right">
                        <a routerLink="/session/forgot-password" class="text-muted">Forgot Password?</a>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <button type="submit" (click)="login()" class="btn btn-primary btn-block btn-lg btn-square">Sign In</button>
            </div>

        </form>
    </div>
</div>
</div-->
