.size-quantity-wrapper {
    .container {
        display: block;
        position: relative;
        padding-left: 35px;
        margin-bottom: 12px;
        cursor: pointer;
        font-size: 22px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Hide the browser's default radio button */
    .container input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    /* Create a custom radio button */
    .checkmark {
        position: absolute;
        top: 0;
        left: 0;
        height: 25px;
        width: 25px;
        background-color: #e59f9f;
        border-radius: 50%;
    }

    /* On mouse-over, add a grey background color */
    .container:hover input ~ .checkmark {
        background-color: #e59f9f;
    }

    /* When the radio button is checked, add a blue background */
    .container input:checked ~ .checkmark {
        background-color: #2196f3;
    }

    /* Create the indicator (the dot/circle - hidden when not checked) */
    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    /* Show the indicator (dot/circle) when checked */
    .container input:checked ~ .checkmark:after {
        display: block;
    }

    /* Style the indicator (dot/circle) */
    .container .checkmark:after {
        top: 9px;
        left: 9px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: white;
    }
    .mr-2 {
        margin-right: 2px;
        width: 60px;
    }
    .rating {
        max-width: 100%;
    }

    .sizeunit {
        font-size: 0.9rem;
        line-height: 16px;
        font-weight: 400;
    }
}

.placeorderbtn {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px;
    background: #ff3f6c;
    cursor: pointer;
    text-align: center;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-left: 10px;
}

.placeorderbtncnl {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px;
    background: grey;
    cursor: pointer;
    text-align: center;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.btncontainer {
    display: flex;
    justify-content: flex-end;
}
.form-group label {
    font-weight: 600;
    margin: 0 0 10px;
    color: #414658;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}
.input-flex-wrap {
    flex-wrap: wrap !important;
    width: 100%;
}

.width48 {
    width: 48%;
}
.width100 {
    width: 100%;
}
.unit-div {
    width: 48%;
}
.rating-wrapper-mobile {
    display: flex;
    flex-direction: column;
}
.lable-font {
    font-weight: bold;
}

.label-width50 {
    width: 80px;
    margin-right: 15px !important;
    white-space: normal;
}
.margin-right-5 {
    margin-right: 5px;
}

// input[type=number] {
//     -moz-appearance:textfield;
// }
