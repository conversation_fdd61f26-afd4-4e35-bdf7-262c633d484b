import { Injectable } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, RoutesRecognized } from '@angular/router';
import { filter, map, switchMap, tap } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Breadcrumb } from './breadcrumb.model';

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbService {
  public crumbs$: Observable<Breadcrumb[]>;
  public activeCrumb: Breadcrumb;
  eachCrumb: any = {};
  crumbs: Breadcrumb[] = [];

  constructor(public activatedRoute: ActivatedRoute, public router: Router) {

    this.crumbs$ = this.router.events.pipe(
      // only continue if routing has completed
      filter(event => event instanceof RoutesRecognized),

      map((event: RoutesRecognized) => event.state.root.firstChild),

      map(snapshot => this.routeSnapshotToBreadcrumb(snapshot)),

      tap(crumbs => this.activeCrumb = crumbs[crumbs.length - 1]),

    );

  }

  private routeSnapshotToBreadcrumb(snapshot: ActivatedRouteSnapshot): any[] {
    this.crumbs = [];
    let routeSnapshot = snapshot;
    let routeFromRoot = '';

    while (routeSnapshot) {
      if (!routeSnapshot.url.length) {
        break;
      }
      let urls: any[] = routeSnapshot.url;
      let urlSegment: any = 'h/'
      urls.forEach(url => {
        if (url) {
          urlSegment = urlSegment + url.path + '/';
        } else {

        }
      })
      const route = `/${urlSegment}`;
      const label = routeSnapshot.data.breadcrumb;
      if (this.crumbs.length == 0) {
        this.crumbs.push({
          'urlSegment': 'Home',
          'route': 'home',
          'label': 'Home'
        });
      }
      this.eachCrumb = {
        urlSegment,
        route,
        label,
      }

      this.crumbs.push(this.eachCrumb);

      routeSnapshot = routeSnapshot.firstChild;
    }
    this.crumbs.forEach((crumb, index) => {
      if (!crumb.label) {
        this.crumbs.splice(index, 1);
      }
    });
    let breadcrumbs: any[] = [...this.crumbs, this.eachCrumb];

    let uniqueArray = breadcrumbs.filter((v, i, a) => a.findIndex(t => (t.route === v.route)) === i);
    return uniqueArray;
  }
}
