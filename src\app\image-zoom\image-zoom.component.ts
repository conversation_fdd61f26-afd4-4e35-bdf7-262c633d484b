import { Component, OnInit, Input, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { Catalog } from 'app/model/catalog.model';
import { OrderItem } from 'app/model/orderitem.model';

@Component({
  selector: 'image-zoom',
  templateUrl: './image-zoom.component.html',
  styleUrls: ['./image-zoom.component.scss']
})
export class ImageZoomComponent implements OnInit {
  currentViewImag: string;
  @Input() images: any[] = [];
  @Input() product: Catalog = new Catalog();
  @Input() purchasedOrderData: OrderItem[];

  dummyImageArray = [
    { url: '/assets/img/galleryV2/1.jpg', alt: 'iamge-1' },
    { url: '/assets/img/galleryV2/2.jpg', alt: 'iamge-2' },
    { url: '/assets/img/galleryV2/3.jpg', alt: 'iamge-3' },
  ]

  modalRef: BsModalRef;
  constructor(private modalService: BsModalService) { }

  ngOnInit(): void {
    console.log("images : ", this.images);
  }

  openModal(template: TemplateRef<any>, image: any) {
    let config = {
      backdrop: true,
      ignoreBackdropClick: false
    }
    this.currentViewImag = image;
    this.modalRef = this.modalService.show(
      template,
      Object.assign({ config }, { class: 'image-popup-wrapper modal-lg' })
    );
  }
  public viewImage(image) {
    this.currentViewImag = image;
  }

  public getTotalQty(): number {
    let totalQty: number = 0;
    for (let i = 0; i < this.purchasedOrderData.length; i++) {
      if (this.purchasedOrderData[i].qty) {
        totalQty += this.purchasedOrderData[i].qty;
      }
    }
    return totalQty;
  }
}
