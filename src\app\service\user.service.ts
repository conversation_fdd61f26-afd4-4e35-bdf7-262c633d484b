import { Injectable } from '@angular/core';

import {HttpClientModule, HttpClient} from '@angular/common/http';
import {HttpEvent, HttpInterceptor, HttpHeaders,  HttpHandler, HttpRequest} from '@angular/common/http';

import {Observable} from 'rxjs';
import { CustomerProfile } from 'app/model/customerprofile.model';
import { User } from 'app/model/user.model';
import { Status } from 'app/model/status.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';

import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UserService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  public getUsers(): Observable<User[]> {
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
     .set("Authorization", localStorage.getItem("basicAuth"));

    return this.http.get<User[]>( `${environment.apiUrl}/api/user/list`, {
        headers:headers
      });
  }

  getUserDetails(): Observable<User> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", localStorage.getItem("basicAuth"));

    return this.http.get<User>(Appconfig.BASE_URL + 'api/user/', {
      headers: headers
    });
  }


  getTenantConfig(user): Observable<TenantConfig> {
    //// let basicAuth = localStorage.getItem('baiscAuth');
    //var user = JSON.parse(localStorage.getItem("userProfile"))
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", localStorage.getItem("basicAuth"));

    return this.http.get<TenantConfig>(Appconfig.BASE_URL + 'api/user/homepageConfig/'+user.tenantUid, {
      headers: headers
    });
  }


  addUser(profile: User): Observable<Status> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", localStorage.getItem("basicAuth"));

    return this.http.put<Status>(`${environment.apiUrl}/api/user/`, profile, {
      headers: headers
    });
  }

}
