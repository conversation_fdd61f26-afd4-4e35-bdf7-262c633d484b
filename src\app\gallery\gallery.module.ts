import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
// import { MatDialogModule, MatDialog, MatDialogConfig } from "@angular/material/dialog";
// import { MatButtonModule } from '@angular/material/button';
import { CarouselModule } from 'ngx-bootstrap/carousel';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { AppMaterialModule } from '../app-material-module';


// import { SlickCarouselModule } from 'ngx-slick-carousel';


import { CatalogFilterComponent } from '../catalog-filter/catalog-filter.component';
import { GalleryListManagementComponent } from './gallery.component';
import { GallerySortComponent } from './gallery-sort/gallery-sort.component';
import { SizeRatioDialogComponent } from './size-ratio/size-ratio-dialog.component';
import { HorizontalGalleryComponent } from './horizontal-gallery/horizontal-gallery.component';
import { SearchbyStyleCodePipe } from './pipes/searchby-style-code.pipe';
import { AddcartDetailsComponent } from './addcart-details/addcart-details.component';

const routes: Routes = [
  {
    path: '',
    component: GalleryListManagementComponent,
  }, {
    path: 'sizeRatio',
    component: SizeRatioDialogComponent,
    data: { breadcrumb: 'Size Ratio' }
  }, {
    path: 'catalogFilter',
    component: CatalogFilterComponent,
    data: { breadcrumb: 'Catalog Filter' }
  }

];



@NgModule({
  declarations: [
    GalleryListManagementComponent,
    CatalogFilterComponent,
    GallerySortComponent,
    SizeRatioDialogComponent,
    CatalogFilterComponent,
    HorizontalGalleryComponent,
    SearchbyStyleCodePipe,
    AddcartDetailsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(routes),
    InfiniteScrollModule,
    CarouselModule.forRoot(),
    LazyLoadImageModule,
    NgbModule,
    AppMaterialModule
    // SlickCarouselModule,
    // MatButtonModule,
    // MatDialogModule,
    // MatDialog,
    // MatDialogConfig
  ],
  // entryComponents: [
  //   SizeRatioDialogComponent
  // ]
  exports: [
    GalleryListManagementComponent,
    HorizontalGalleryComponent,
    CatalogFilterComponent
  ]
})
export class GalleryListManagementModule { }
