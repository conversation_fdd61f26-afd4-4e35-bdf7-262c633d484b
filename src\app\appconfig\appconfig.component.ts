import { Component, Injectable, OnInit } from '@angular/core';
import { Observable, Subject } from 'rxjs';

import { User } from '../model/user.model';


@Component({
  selector: 'app-appconfig',
  templateUrl: './appconfig.component.html',
  styleUrls: ['./appconfig.component.scss']
})
export class AppconfigComponent implements OnInit {

  constructor() { }

  ngOnInit() {
  }

}


export const Appconfig = Object.freeze({
  // BASE_URL: 'http://localhost:9191/',
  // BASE_URL: 'http://asusegr01.3frameslab.com:9292/',
  // BASE_URL: 'https://asusegr01.3frameslab.com/', // Citrus, IDC, Stori, Twills
  // BASE_URL: 'https://asusegr02.3frameslab.com/', // <PERSON><PERSON>, Turtle
  // BASE_URL: 'https://l0x3krvkrf.execute-api.ap-south-1.amazonaws.com/', // USPA, G&J, LifeStyle,re, Indian Terrain
  // BASE_URL: 'https://aspada.3frameslab.com/', // Test
  // BASE_URL: 'https://asapsgr01.3frameslab.com/', // USPA, G&J, LifeStyle
  // BASE_URL: 'https://aspada.3frameslab.com/',
//      BASE_URL: 'https://palmsrv.3frameslab.com/', // palm tree
  BASE_URL: 'https://77dh1v6l1e.execute-api.ap-south-1.amazonaws.com/',
  //IMAGE_URL:'http://uspa.3frameslab.com/bom/',
  //IMAGE_URL:'http://uspa.3frameslab.com/bom/',
  CHAT_IMAGE_URL: 'https://cocreate.arvindbrands.com/srv/web/catalog/download/',
  PLACEHOLDER_IMG: 'http://uspa.3frameslab.com/bom/placeholder.png',
  //  BASE_URL: 'http://************:8080/',
  //  BASE_URL: 'http://************:8080/',
  CHAT_BASE_URL: 'https://cocreate.arvindbrands.com/srv/web',
  CHAT_PORTAL_URL: 'https://cocreate.arvindbrands.com/srv/web',

});


@Injectable()
export class Globals {
  public activeTab: string = 'dashboard';
  //public basicAuth: string = 'Basic ' + btoa('shilpa' + ':' + 'shilpa');
  public basicAuth: string = '';//Basic ' + btoa('8000:8000');
  public $basicAuth = new Subject<any>();
  public user: User = new User();
  public tenantUid: string;

  setBasicAuth(basic) {
    this.basicAuth = basic;
    localStorage.setItem('basicAuth', basic);
    this.$basicAuth.next(localStorage.getItem('basicAuth'));
  }

}


@Injectable()
export class MessageService {
  private event = new Subject<any>();

  sendMessage(message: any) {
    this.event.next({ message: message });
  }

  clearMessage() {
    this.event.next();
  }

  getMessage(): Observable<any> {
    return this.event.asObservable();
  }
}
