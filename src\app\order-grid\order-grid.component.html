<div class="ecommerce-product-cart-wrapper maincontainer">
  <div *ngIf="myOrders != undefined && myOrders.length > 0" class="row">
    <div class="col-sm-12 col-md-12 col-lg-12 col-xl-12 mt-4">
      <div class="col-md-2 form-inline dropdown">
        <div class="form-group" style="width: 100%;">
          <!--           <select class="form-control" id="formSelect1" (change)="refreshOrders()" [(ngModel)]="selectedOrderStatus">
            <option value="-1">All</option>
            <option value="1">In-Progress</option>
            <option value="2">Completed</option>
          </select> -->
        </div>
      </div>

      <ul ngbNav #nav="ngbNav" class="nav-tabs" style="display: flex;justify-content: space-around;width:100%">
        <li ngbNavItem>
          <a ngbNavLink>All</a>
          <ng-template ngbNavContent>
            <div class="itemheader">
              <div>Total Orders -All</div>
              <div>{{currentShowingOrders}}</div>
              <div class="totalitem">({{myOrders.length}} order's)</div>
            </div>
            <div class="itemcontainer" *ngFor="let item of myOrders">
              <div class="innerItemContainer" (click)="viewOrder(item.orderUid)">
                <div>
                  <div><strong>Order Id &nbsp;: &nbsp;{{item.orderUid}}</strong></div>
                  <div><strong>Customer Name &nbsp;: &nbsp;{{item.customerName}}</strong></div>
                  <div>Quantity &nbsp;: &nbsp;{{item.qty}}</div>
                  <div *ngIf="item.createdTime && item.createdTime>0">Date of Created &nbsp; : &nbsp;{{displayDate(item.createdTime)}}</div>
                  <div *ngIf="item.submittedOn && item.submittedOn>0">Date of submission &nbsp; : &nbsp;{{displayDate(item.createdTime)}}</div>
                </div>  
                <div class="icon-div">
                  <i class="fa fa-angle-double-right"></i>
                </div>
              </div>
            </div>

          </ng-template>
        </li>
        <li ngbNavItem>
          <a ngbNavLink>In-Progress</a>
          <ng-template ngbNavContent>
            <div class="itemheader">
              <div>Total Orders -Pending</div>
              <div>{{currentShowingOrders}}</div>
              <div class="totalitem">({{inProgressOrders?.length}} order's)</div>
            </div>
            <div class="itemcontainer" *ngFor="let item of inProgressOrders">
              <div class="innerItemContainer" (click)="viewOrder(item.orderUid)">
                <div>
                  <div><strong>Order Id &nbsp;: &nbsp;{{item.orderUid}}</strong></div>
                  <div><strong>Customer Name &nbsp;: &nbsp;{{item.customerName}}</strong></div>
                  <div>Quantity &nbsp;: &nbsp;{{item.qty}}</div>
                  <div *ngIf="item.createdTime && item.createdTime>0">Date of Created &nbsp; : &nbsp;{{displayDate(item.createdTime)}}</div>
                </div>  
                <div class="icon-div">
                  <i class="fa fa-angle-double-right"></i>
                </div>
              </div>
            </div>

          </ng-template>
        </li>
        <li ngbNavItem>
          <a ngbNavLink>Completed</a>
          <ng-template ngbNavContent>
            <div class="itemheader">
              <div>Number Of Orders - Submitted</div>
              <div>{{currentShowingOrders}}</div>
              <div  class="totalitem">({{completedOrders?.length}} order's)</div>
            </div>
            <div class="itemcontainer" *ngFor="let item of completedOrders">
              <div  class="innerItemContainer" (click)="viewOrder(item.orderUid)">
                <div>
                  <div><strong>Order Id &nbsp;: &nbsp;{{item.orderUid}}</strong></div>
                  <div><strong>Customer Name &nbsp;: &nbsp;{{item.customerName}}</strong></div>
                  <div>Quantity &nbsp;: &nbsp;{{item.qty}}</div>
                  <div *ngIf="item.createdTime && item.createdTime>0">Date of Submitted &nbsp; : &nbsp;{{displayDate(item.createdTime)}}</div>
                </div>  
                <div class="icon-div">
                  <i class="fa fa-angle-double-right"></i>
                </div>
              </div>
            </div>

          </ng-template>
        </li>
      </ul>
      <div [ngbNavOutlet]="nav"></div>

      
    </div>
  </div>
  <div *ngIf="myOrders && myOrders.length == 0">
    <h4 class="mt-5 mb-4 alert alert-success" style="background-color: #dddddd;color: black;"> Your orders is
      empty.
    </h4>
  </div>
  <div *ngIf="myOrders == undefined">
    <h4 class="mt-5 mb-4 alert alert-success" style="background-color: #dddddd;color: black;"> Fetching ...
    </h4>
  </div>
</div>