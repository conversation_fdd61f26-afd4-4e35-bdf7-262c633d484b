export const environment = {
  production: true,
  //apiUrl: 'http://uspa.3frameslab.com:8090',
  // apiUrl: 'https://asapsgr01.3frameslab.com', // uspa
  //apiUrl: 'https://usiw.3frameslab.com',
  //  apiUrl: 'https://asusegr01.3frameslab.com', // Citrus, IDC, Stori, Twills
  apiUrl: 'https://asusegr02.3frameslab.com', // usiw(Hanes), turtle
  // apiUrl: 'https://asapsgr01.3frameslab.com', // USPA, G&J, LifeStyle, re, Indian Terrain
  // apiUrl: 'https://aspada.3frameslab.com', // Test
//     apiUrl: 'https://palmsrv.3frameslab.com', // palm tree
  // apiUrl: 'https://l0x3krvkrf.execute-api.ap-south-1.amazonaws.com',

//   tenantUid:'storiroadshow',
  //tenantUid:'idc',
  // tenantUid:'citrus',
  // tenantUid:'twillsroadshow',
  // tenantUid:'lfs',
  // tenantUid:'uspa',
//   tenantUid:'gaj',
//   tenantUid:'gajreadystock',
  // tenantUid:'uspaaw21',
  // tenantUid:'usiw', //hanes
  // tenantUid:'test',
  // tenantUid:'ul',
//     tenantUid:'ptreadystock',
    tenantUid:'turtlereadystock',
    // tenantUid: 'turtleroadshow',
    // tenantUid:'turtle',
// tenantUid:'arvind',
      // tenantUid:'indt',
  readyStockTenantUid: 'gajreadystock',
  title: ' Roadshow SS21'
};
