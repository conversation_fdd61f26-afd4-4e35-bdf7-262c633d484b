import { Component, OnInit, ElementRef, Renderer2, ViewChild } from '@angular/core';

import { ToastrService, ToastrConfig } from 'ngx-toastr';
import { Router, NavigationEnd } from '@angular/router';
import { AppconfigComponent, Appconfig, Globals } from '../appconfig/appconfig.component';

import { OrderService } from 'app/service/order.service';
import { WindowRefService } from 'app/service/window-ref.service';
import { Status } from 'app/model/status.model';
import { PaymentInfo } from 'app/model/PaymentInfo.model';

@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss'],
  providers: [WindowRefService]
})
export class PaymentComponent implements OnInit {

  constructor(private winRef: WindowRefService, private router: Router, private globals:Globals, private orderService: OrderService ) {}

  ngOnInit() {
    this.createRzpayOrder()
   //this.router.navigate(['/h/home']);
  }


  createRzpayOrder() {
    this.orderService.initPayment().subscribe(paymentInfo => {
      this.payWithRazor(paymentInfo);
    });
    
  }

  payWithRazor(paymentInfo) {
    const options: any = {
      key: paymentInfo.apiKey,
      amount: paymentInfo.amount, 
      currency: paymentInfo.currency,
      name: paymentInfo.customerName,
      description: paymentInfo.description,
      image: './assets/logo.png',
      order_id: paymentInfo.paymentOrderUid,
      modal: {
        escape: false,
      },
      notes: {
      },
      theme: {
        color: '#0c238a'
      }
    };
    options.handler = ((response, error) => {
      options.response = response;
      var payment = new PaymentInfo();
      payment.paymentId = response.razorpay_payment_id;
      payment.paymentOrderUid = response.razorpay_order_id;
      payment.signature = response.razorpay_signature;
      this.orderService.verifyPayment(payment).subscribe(paymentInfo => {
        this.router.navigate(['/h/home']);
      });  
    });
    options.modal.ondismiss = (() => {
      // handle the case when user closes the form while transaction is in progress
    });
    const rzp = new this.winRef.nativeWindow.Razorpay(options);
    rzp.open();
  }

}