<!-- <ng-progress></ng-progress> -->
<div class="global-loader" *ngIf="showLoadingIndicator">
	<div class="innerSpinner">
		LOADING...
		<span class="innerSpinner1"></span>
	</div>
</div>

<div class="app-harizontal-wrapper">
	<nav class="horizontal-nav">
		<div>
			<mat-toolbar class="main-top-menu-row">
				<mat-toolbar-row>
					<button mat-icon-button (click)="sidenav.toggle()" fxShow="true" fxHide.gt-sm
						class="vertical-navigation-menu">
						<mat-icon>menu</mat-icon>
					</button>
					<div class="d-flex justify-content-between align-items-center width100 ">
						<div fxShow="true" fxHide.lt-md>
							<ul class="horizontal-navigation-menu clearfix mb-0">
								<li routerLink="home"
									style="cursor: pointer; margin-right: 30px; margin:0px !important; font-size:14px; padding: 4px;">
									<div class="logo-sign">
										<!-- <h1 class="brand-class">LOGO</h1> -->
										<img class="img-responsive" [src]="tenantLogo" alt=""
											style="width: 250px;height: 80px;">
									</div>
								</li>
								<!-- <li routerLinkActive="active-link" routerLink="home">
									<a>{{ 'Home' | translate }}</a>
								</li> -->
								<li routerLinkActive="active-link" *ngFor="let item of level1; index as i;"
									class="menu-item">
									<a class="menu-link-item mainmenu"> {{item.name}}</a>
									<div class="child-menu">
										<div class="child-menu-column"
											*ngFor="let item2 of level2">
											<div class="column">
												<strong class="category-heading">{{ item2.name | translate }}</strong>
												<ul style="list-style-type: none;" class="column">
													<li class="category"
														*ngFor="let item3 of getProductCounts(level3, item2.code)">
														<a *ngIf="item3.count!=0" class="category-link" routerLinkActive="active-link"
															(click)="setListMode(item3)">{{ item3.name | translate
															}}&nbsp;<span style="color:#8f0909;font-size: 12px">({{
																item3.count}})</span></a>
													</li>
												</ul>
											</div>

										</div>
									</div>
								</li>
								<button *ngIf="!isRetailer()" (click)="import(importOrderPopup)"
									class="btn btn-primary ml-2 btn-sm" type="button" style="float:right"><span
										class="glyphicon glyphicon-cloud-upload"></span>Import</button>
								<li *ngIf="tenantConfig.enableAdvanceFeatures==1" class="menu-item"
									routerLinkActive="active-link" routerLink="videoconf">
									<a class="menu-link-item">{{ 'Video Rooms' | translate }}</a>
								</li>
								<li *ngIf="tenantConfig.enableAdvanceFeatures==1" class="menu-item"
									routerLinkActive="active-link" routerLink="videostream">
									<a class="menu-link-item">{{ 'Live Video Streaming' | translate }}</a>
								</li>
								<li *ngIf="tenantConfig.enableAdvanceFeatures==1" class="menu-item"
									routerLinkActive="active-link" routerLink="livetour">
									<a class="menu-link-item">{{ '360 Tour & Booking' | translate }}</a>
								</li>

							</ul>
						</div>
						<div>
							<div style="display: flex;align-items: center;">
								<p class="locationAndname" style="margin:0">{{tenantHomeConfigName}} <b *ngIf="userLocation">({{userLocation}})</b></p>
							</div>
						</div>
						<div class="align-items-end pr-0 d-flex horizontal-top-menu">
							<div class="d-flex align-self-center secondary-menu">
								<ul class="nav secondary-menu-list d-flex justify-content-end align-items-center"
									data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">
									<li class="d-none d-md-block fullscreen-toggle" (click)="toggleFullscreen()">
										<a href="javascript:void(0)" class="zoom-out">
											<i class="material-icons" *ngIf="!isFullscreen">tv</i>
											<i class="material-icons" *ngIf="isFullscreen">fullscreen_exit</i>
										</a>
									</li>
									<!-- <a href="javascript:void(0)" class="zoom-out">
										<span>{{cartCount}}</span>
									</a>
									<a href="javascript:void(0)" class="zoom-out">
										<span>{{wishlistCount}}</span>
									</a> -->
									<li class="d-none d-md-block fullscreen-toggle" (click)="openCart()"
										title="My cart">
										<a href="javascript:void(0)" class="zoom-out">
											<i class="fa fa-shopping-cart" aria-hidden="true"></i>
											<small class="qtyIndicator" *ngIf="cartCount!=0">{{cartCount}}</small>
										</a>
									</li>

									<li class="d-none d-md-block fullscreen-toggle" (click)="openWishlist()"
										title="My Wishlist">
										<a href="javascript:void(0)" class="zoom-out">
											<mat-icon>turned_in</mat-icon>
											<small class="qtyIndicator"
												*ngIf="wishlistCount!=0">{{wishlistCount}}</small>
										</a>
									</li>

									<!-- <li class="d-none d-md-block dropdown">
										<a href="javascript:void(0)" id="navbarDropdownMenu" data-toggle="dropdown"
											aria-haspopup="true" aria-expanded="false"><i class="fa fa-shopping-cart"
												aria-hidden="true"></i> <span
												class="fa fa-circle fa-notify text-primary dot-note"></span></a>
										<div class="dropdown-menu rounded-0 dropdown-menu-right dropdown-menu-lg p-0"
											aria-labelledby="navbarDropdownMenu">
											<div class="dropdown-header bg-primary d-flex justify-content-between"><span
													class="align-self-center">12
													Items in cart</span><a (click)="payment()"
													class="align-self-center">Payment</a>
											</div>
											<a class="dropdown-item  d-flex pt-3" href="javascript:void(0)">
												<div class="icon-area  align-self-start">
													<i class="fa fa-envelope-o"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'12 options in USTSH '|translate}}</span>
													<small>12 min ago</small>
												</div>
											</a>
											<a class="dropdown-item  d-flex" href="javascript:void(0)">
												<div class="icon-area align-self-start">
													<i class="fa fa-clock-o" aria-hidden="true"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'20 options in USTRO'|translate}}</span>
													<small>12 min ago</small>
												</div>
											</a>
										</div>
									</li> -->
									<!-- <li class="d-none d-md-block dropdown">
										<a href="javascript:void(0)" id="navbarDropdownMenu" data-toggle="dropdown"
											aria-haspopup="true" aria-expanded="false"><i class="fa fa-shopping-cart"
												aria-hidden="true"></i> <span
												class="fa fa-circle fa-notify text-primary dot-note"></span></a>
										<div class="dropdown-menu rounded-0 dropdown-menu-right dropdown-menu-lg p-0"
											aria-labelledby="navbarDropdownMenu">
											<div class="dropdown-header bg-primary d-flex justify-content-between"><span
													class="align-self-center">12
													Items in cart</span><a (click)="payment()"
													class="align-self-center">Payment</a>
											</div>
											<a class="dropdown-item  d-flex pt-3" href="javascript:void(0)">
												<div class="icon-area  align-self-start">
													<i class="fa fa-envelope-o"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'12 options in USTSH '|translate}}</span>
													<small>12 min ago</small>
												</div>
											</a>
											<a class="dropdown-item  d-flex" href="javascript:void(0)">
												<div class="icon-area align-self-start">
													<i class="fa fa-clock-o" aria-hidden="true"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'20 options in USTRO'|translate}}</span>
													<small>12 min ago</small>
												</div>
											</a>
										</div>
									</li>	 -->
									<!-- <li class="d-none d-md-block dropdown">
										<a href="javascript:void(0)" class="open-box" id="navbarDropdownMen"
											data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i
												class="material-icons msg-close">mail_outline</i><i
												class="fa fa-envelope-open-o"></i><span
												class="fa fa-circle fa-notify dot-note text-danger"></span></a>
										<div class="dropdown-menu rounded-0 dropdown-menu-right dropdown-menu-lg p-0"
											aria-labelledby="navbarDropdownMen">
											<div class="dropdown-header bg-warning d-flex justify-content-between"><span
													class="align-self-center">15
													total messages</span><a href="javascript:void(0)"
													class="align-self-center">View All</a>
											</div>
											<a class="dropdown-item  d-flex pt-3" href="javascript:void(0)">
												<div class="icon-area  align-self-start">
													<i class="fa fa-envelope-o"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'Inbox'|translate}}</span>
													<small>15 new messages</small>
												</div>
											</a>
											<a class="dropdown-item  d-flex" href="javascript:void(0)">
												<div class="icon-area  align-self-start">
													<i class="fa fa-gift" aria-hidden="true"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'Promotion
														messages'|translate}}</span>
													<small>5 messages</small>
												</div>
											</a>
											<a class="dropdown-item  d-flex" href="javascript:void(0)">
												<div class="icon-area align-self-start">
													<i class="fa fa-exclamation-circle" aria-hidden="true"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'Spam
														message'|translate}}</span>
													<small>12 min ago</small>
												</div>
											</a>
											<a class="dropdown-item  d-flex pb-3" href="javascript:void(0)">
												<div class="icon-area align-self-start">
													<i class="fa fa-bullhorn" aria-hidden="true"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'Social
														Media'|translate}}</span>
													<small>34 new messages</small>
												</div>
											</a>
										</div>
									</li> -->
									<li class="dropdown btndrdn">
										<a class="relative rounded-bg" href="javascript:void(0)"
											id="navbarDropdownMenuLin" data-toggle="dropdown" aria-haspopup="true"
											aria-expanded="false">
											<!-- <span><i style="font-size: 45px;" class="fa fa-user-circle"></i></span> -->
											<img class="rounded-circle" src="assets/img/user_logo.jpg" alt="User-image"
												width="45" height="45">
											<div class="online-caret bg-primary circle"><i class="fa fa-caret-down"></i>
											</div>
										</a>
										<div class="dropdown-menu rounded-0 dropdown-menu-right p-0"
											aria-labelledby="navbarDropdownMenuLin">
											<!-- <a class="dropdown-item  d-flex pt-3">
												<div class="icon-area  align-self-start">
													<i class="fa fa-user"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'My
														Profile'|translate}}</span>
												</div>
											</a> -->
											<!-- <a class="dropdown-item  d-flex">
												<div class="icon-area align-self-start">
													<mat-icon>turned_in</mat-icon>
												</div>
												<div class="icon-msg mx-2"> <span routerLink="wishlistGrid"
														class="align-self-start d-block ">{{'My Wishlist'|translate}}</span>
												</div>
											</a> -->

											<a class="dropdown-item  d-flex">
												<div class="icon-area align-self-start">
													<i class="fa fa-money"></i>
												</div>
												<div class="icon-msg mx-2"> <span routerLink="orderGrid"
														class="align-self-start d-block ">{{'My
														Orders'|translate}}</span>
												</div>
											</a>

											<a class="dropdown-item  d-flex">
												<div class="icon-area align-self-start">
													<i class="fa fa-question-circle"></i>
												</div>
												<div class="icon-msg mx-2">
														<a (click)="needHelp(supportNumTemplate)" class="align-self-start d-block ">{{'Help&support'|translate}} </a><span class="clearfix"></span>
												</div>
											</a>


											<a class="dropdown-item  d-flex pb-2" (click)="logOut()">
												<div class="icon-area align-self-start">
													<i class="fa fa-power-off"></i>
												</div>
												<div class="icon-msg mx-2"> <span
														class="align-self-start d-block ">{{'Logout'|translate}}</span>
												</div>
											</a>
										</div>
									</li>
								</ul>
							</div>
						</div>
					</div>

				</mat-toolbar-row>
			</mat-toolbar>
			<mat-sidenav-container fxFlexFill style="height: 100vh;">
				<mat-sidenav #sidenav class="vertical-navigation-menu" style="width: 70%;">

					<!-- <mat-nav-list>
							 <a (click)="sidenav.toggle()" href="" mat-list-item>Close</a>		
					</mat-nav-list> -->

					<mat-nav-list style="background: gainsboro;">
						<div style="display: flex;justify-content: space-around;">
							<!-- <a (click)="sidenav.toggle()" href="" mat-list-item>Close</a> -->

							<a (click)="openCart()" href="javascript:void(0)" class="zoom-out">
								<i style="font-size: 34px;" class="fa fa-shopping-cart" aria-hidden="true"></i>
							</a>
							<a (click)="openWishlist()" href="javascript:void(0)" class="zoom-out">
								<mat-icon style="font-size: 34px;">turned_in</mat-icon>
							</a>
							<a class="relative rounded-bg" href="javascript:void(0)" id="navbarDropdownMenuLin"
								data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
								<span><i style="font-size: 45px;" class="fa fa-user-circle"></i></span>
								<!-- <img class="rounded-circle" src="assets/img/user-3.jpg" alt="User-image" width="34"
									height="34"> -->
								<div class="abc"><i class="fa fa-caret-down icn"></i>
								</div>
							</a>
							<div class="dropdown-menu rounded-0 dropdown-menu-right p-0"
								aria-labelledby="navbarDropdownMenuLin">
								<a class="dropdown-item  d-flex">
									<div class="icon-area align-self-start">
										<i class="fa fa-money"></i>
									</div>
									<div class="icon-msg mx-2"> <span routerLink="orderGrid"
											class="align-self-start d-block ">{{'My Orders'|translate}}</span>
									</div>
								</a>

								<a class="dropdown-item  d-flex pb-2" (click)="logOut()">
									<div class="icon-area align-self-start">
										<i class="fa fa-power-off"></i>
									</div>
									<div class="icon-msg mx-2"> <span
											class="align-self-start d-block ">{{'Logout'|translate}}</span>
									</div>
								</a>
							</div>
						</div>
						<ul class="verticalNavigationClass">
						</ul>
					</mat-nav-list>

					<mat-nav-list>
                        <ng-template #supportNumTemplate>
							<div class="modal-dialog custom-dialog">
							  <div class="modal-content">
								<div class="modal-header">
								  <h4 class="modal-title">
									Need Help?
								  </h4>
								  <button type="button" class="close" aria-label="Close" (click)="modalRefData.hide()">
									<span class="glyphicon glyphicon-remove">×</span>
								  </button>
								</div>
						  
								<div class="modal-body">
								  <h3>
									 Use below contact number to connect with support
								  </h3>
								  <h3 class="text-primary" id="mblNum">{{supportNum}}</h3>
								</div>
							  </div>
							</div>
						  </ng-template>

						<ul id="accordion" class="accordion">
							<li class="accordion1" *ngFor="let item of level1; index as i;"
								[class.active]="item.active">
								<div class="menu" (click)="toggle('level1' , item.code)">
									{{ item.name }}
									<i class="fa fa-chevron-down"></i>
								</div>
								<ul class="submenu1">
									<li class="accordion2" *ngFor="let item2 of level2"
										[class.active]="item2.active">
										<div class="menu" (click)="toggle('level2' , item2.code )">
											{{ item2.name }}
											<i class="fa fa-chevron-down"></i>
										</div>
										<ul class="submenu2">
											<li class="accordion3" *ngFor="let item3 of getProductCounts(level3, item2.code)"
												(click)="setListMode(item3)" [class.active]="item3.active">
												<div class="menu">
													{{ item3.name }}({{item3.count}} )
												</div>
											</li>
										</ul>
									</li>
								</ul>
							</li>
						</ul>


						<!-- <li routerLinkActive="active-link" *ngFor="let item of level1; index as i;" class="menu-item">
							<a style="font-size: 22px;font-weight: 700; color: #818181;" class="menu-link-item">
								{{item.name}}</a>
							<div class="child-menu">
								<div class="child-menu-column" *ngFor="let item2 of getChilds(level2, item.code)">
									<div class="column">
										<strong class="category-heading">{{ item2.name | translate }}</strong>
										<ul style="list-style-type: none;" class="column">
											<li class="category" *ngFor="let item3 of getChilds(level3, item2.code)">
												<a class="category-link" routerLinkActive="active-link"
													(click)="setListMode(item3)">{{ item3.name | translate }}&nbsp;<span
														style="color:#8f0909;font-size: 12px">({{
														item3.count}})</span></a>
											</li>
										</ul>
									</div>

								</div>
							</div>
						</li> -->
						<ul class="verticalNavigationClass">

						</ul>
					</mat-nav-list>

				</mat-sidenav>

				<mat-sidenav-content fxFlexFill style="height: 100%;margin-bottom: 100px;">
					<div class="chankya-base-container">
						<div class="inner-container">
							<!-- <app-breadcrumb></app-breadcrumb> -->
							<router-outlet></router-outlet>
						</div>
					</div>
				</mat-sidenav-content>
			</mat-sidenav-container>
		</div>
	</nav>
</div>

<ng-template #importOrderPopup let-modal>
	<div class="modal-header">
		<h4 class="modal-title" id="modal-basic-title">Import Order</h4>
		<button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
			<span aria-hidden="true">×</span>
		</button>
	</div>
	<div class="modal-body">
		<form class="mb-4 form-obo">

			<div class="center" *ngIf="files.length==0">
				<ngx-file-drop dropZoneLabel="Drop updated order files here" (onFileDrop)="setFiles($event)"
					(onFileOver)="fileOver($event)" (onFileLeave)="fileLeave($event)">
					<ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
						Drop Style Excel Sheet here.
						<div class="file-field" style="margin-left: 10px; background-color: #0f3f4ab8;">
							<div class="btn btn-outline-success btn-rounded waves-effect btn-sm float-left"
								(click)="openFileSelector()"><span style="color:white">Browse Files</span></div>
						</div>
					</ng-template>
				</ngx-file-drop>
			</div>

			<div class="center" *ngIf="files.length>0">
				<p *ngFor="let item of files; let i=index">
					<span><strong>{{ item.relativePath }}</strong><span (click)="deleteFile(i)"
							class="glyphicon glyphicon-remove"></span>
						<!--button class="ui-button-success" pButton icon="fa fa-check"
				(click)="deleteFile(i)" label="X"></button-->
					</span>
				</p>
			</div>

			<div class="center" style="margin-top: 20px;">{{status.message}}</div>

		</form>
	</div>
	<div class="modal-footer">
		<!-- <button type="button" class="btn btn-outline-dark" (click)="modal.close('Save click')">Ok</button> -->
		<button type="button" class="btn btn-primary" (click)="modalRef.hide()">Cancel</button>
		<button type="button" (click)="importOrder()" class="btn btn-primary">Import</button>
	</div>
</ng-template>