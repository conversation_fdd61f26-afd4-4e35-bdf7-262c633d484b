import { Component, OnInit, ElementRef, Renderer2, ViewChild } from '@angular/core';

import { ToastrService, ToastrConfig } from 'ngx-toastr';
import { Router, NavigationEnd } from '@angular/router';
import { AppconfigComponent, Appconfig, Globals } from '../appconfig/appconfig.component';

import { User } from '../model/user.model';
import { Catalog } from '../model/catalog.model';
import { CatalogService } from '../service/catalog.service';
import { UserService } from '../service/user.service';

import { Order } from '../model/order.model';
import { OrderItem } from '../model/orderitem.model';
import { TenantConfig } from 'app/model/TenantConfig.model';

import { BsModalService, ModalModule } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';

import { DragScrollComponent } from "ngx-drag-scroll";
import { AccessibilityConfig, Image, ImageEvent } from '@ks89/angular-modal-gallery';
import { NgbCarouselConfig } from '@ng-bootstrap/ng-bootstrap';




@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {




  @ViewChild('nav', { read: DragScrollComponent }) headerRowScrolls: DragScrollComponent;

  @ViewChild('featuredProdsNav', { read: DragScrollComponent }) featuredProdsDS: DragScrollComponent;
  @ViewChild('promotionsNav', { read: DragScrollComponent }) promotionsDS: DragScrollComponent;

  featuredProds: Catalog[] = [];
  activeFeaturedProds: Catalog[] = [];
  fromFeaturedProdIndex: number = 0;
  toFeaturedProdIndex: number = 5;

  newArrivals: Catalog[] = [];
  promotions: Catalog[] = [];
  tenantConfig: TenantConfig = new TenantConfig();

  leftNavDisabled = false;
  rightNavDisabled = false;
  secondRowImages: any = [
    { img: '/assets/img/home-second-row-1.webp', description: '' },
    { img: '/assets/img/home-second-row-2.webp', description: '' },
    { img: '/assets/img/home-second-row-3.webp', description: '' }
  ]
  thirdRowImages: any = [
    {
      image: 'assets/img/home-third-row-1.webp',
      thumbImage: 'assets/img/home-third-row-1.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-2.webp',
      thumbImage: 'assets/img/home-third-row-2.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-3.webp',
      thumbImage: 'assets/img/home-third-row-3.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-4.webp',
      thumbImage: 'assets/img/home-third-row-4.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-5.webp',
      thumbImage: 'assets/img/home-third-row-5.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-1.webp',
      thumbImage: 'assets/img/home-third-row-1.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-2.webp',
      thumbImage: 'assets/img/home-third-row-2.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-3.webp',
      thumbImage: 'assets/img/home-third-row-3.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-4.webp',
      thumbImage: 'assets/img/home-third-row-4.webp',
      alt: 'alt of image',
      title: 'title of image'
    },
    {
      image: 'assets/img/home-third-row-5.webp',
      thumbImage: 'assets/img/home-third-row-5.webp',
      alt: 'alt of image',
      title: 'title of image'
    }
  ]


  //@ViewChild(NgxGalleryComponent) ngxGalleryComponent;


  // firstRowImages: Image[] = [
  //   // new Image(
  //   //   0,
  //   //   {
  //   //     img: '/assets/img/uspa.jpg',
  //   //     description: 'Loading...'
  //   //   },

  //   // ),
  //   // new Image(
  //   //   0,
  //   //   {
  //   //     img: '/assets/img/uspa1.jpeg',
  //   //     description: 'Loading...'
  //   //   },

  //   // ),
  //   // new Image(
  //   //   0,
  //   //   {
  //   //     img: '/assets/img/uspa2.jpeg',
  //   //     description: 'Loading...'
  //   //   },

  //   // ),
  //   // new Image(
  //   //   0,
  //   //   {
  //   //     img: '/assets/img/uspa3.jpg',
  //   //     description: 'Loading...'
  //   //   },

  //   // ),
  //   // new Image(
  //   //   0,
  //   //   {
  //   //     img: '/assets/img/uspa4.jpeg',
  //   //     description: 'Loading...'
  //   //   },

  //   // )
  // ];

  firstRowImages = [
    // {
    //   'img': '/assets/carousel/1.jpg',
    //   'name': 'USPA'
    // },
    // {
    //   'img': '/assets/carousel/2.jpg',
    //   'name': 'USPA'
    // },
    // {
    //   'img': '/assets/carousel/3.png',
    //   'name': 'USPA'
    // },
    // {
    //   'img': '/assets/carousel/4.jpg',
    //   'name': 'USPA'
    // }
  ]

  headerImages: Array<object> = [{
    image: 'assets/img/slider/1.jpg',
    thumbImage: 'assets/img/slider/1_min.jpeg',
    alt: 'alt of image',
    title: 'title of image'
  }
  ];

  constructor(private router: Router, private globals: Globals, private userService: UserService, config: NgbCarouselConfig) {
    config.interval = 1000;
    config.keyboard = false;
    config.pauseOnHover = true;
  }

  ngOnInit() {

    this.getConfig();



  }



  getConfig() {
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    var items = JSON.parse(this.tenantConfig.firstRowImages);
    console.log("images: ", items);
    this.firstRowImages = items;
    // this.firstRowImages = new Array<Image>();
    // var i = 0
    // for (var item of items) {
    //   var img = new Image(i++, { img: item.img, description: '' });
    //   this.firstRowImages = [... this.firstRowImages, img];
    // }
    // this.headerImages = new Array<object>();
    // items = JSON.parse(this.tenantConfig.headerImages)
    // for (var item of items) {
    //   // this.headerImages = [... this.headerImages, { image: item.img, thumbImage: item.img }];
    // }


  }



  setActiveMode(mode) {
    this.globals.activeTab = mode;
    this.router.navigate(['/' + mode]);
  }

  routeToCategory(cat: string) {
    this.globals.activeTab = 'collList/*';
    this.router.navigate(['/productList/Garment/' + cat]);
  }


  selectPromotion(item: Catalog) {

    this.globals.activeTab = 'collList/*';
    this.router.navigate(['/storyList/' + item.productStory]);
  }

  selectFeatured(item: Catalog) {
    this.router.navigate(['/productDetails/' + item.uuid]);
  }

  moveLeft() {
    console.log("calling move left");
    this.headerRowScrolls.moveLeft();
  }

  moveRight() {
    console.log("calling move right");
    this.headerRowScrolls.moveRight();
  }

  moveTo(index) {
    console.log("calling move index");
    this.headerRowScrolls.moveTo(index);
  }

  ngAfterViewInit() {
    // Starting ngx-drag-scroll from specified index(3)
    // setTimeout(() => {
    //   this.headerRowScrolls.moveTo(3);
    // }, 0);
  }

}
