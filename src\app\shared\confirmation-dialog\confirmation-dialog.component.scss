/* Dialog styling */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 8px;

  h2 {
    margin: 0;
  }

  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 24px;
    line-height: 1;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }

    &:focus {
      outline: none;
    }

    .close-icon {
      font-weight: bold;
    }
  }
}

.mat-dialog-actions {
  justify-content: flex-end;
  margin-top: 20px;
}

.mat-dialog-content {
  max-height: 65vh;
}

// Common styles for form fields
.ship-to-field,
.expiry-date-field,
.remarks-field {
  margin-top: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .form-control {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    margin-bottom: 10px;

    &:focus {
      outline: none;
      border-color: #3f51b5;
      box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
    }
  }

  .character-count {
    font-size: 12px;
    color: #666;
    text-align: right;
    margin-top: -8px;
    margin-bottom: 10px;
  }
}

// Ship To field specific styles
.ship-to-field {
  .ship-to-container {
    display: flex;
    gap: 10px;
    align-items: center;

    select {
      flex: 1;
    }

    .add-new-button {
      background-color: #f0f0f0;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 8px 12px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #e0e0e0;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
      }
    }
  }

  .loading-message {
    color: #666;
    font-style: italic;
    padding: 8px 0;
  }

  .error-message {
    color: #d32f2f;
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;

    .retry-button {
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #d32f2f;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
      }
    }
  }

  .new-ship-to-form {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;

    .create-error-message {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .close-error-button {
        background: none;
        border: none;
        color: #856404;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        padding: 0;
        margin-left: 10px;

        &:hover {
          color: #533f03;
        }

        &:focus {
          outline: none;
        }
      }
    }

    .form-row {
      margin-bottom: 15px;

      &.two-columns {
        display: flex;
        gap: 15px;

        .form-group {
          flex: 1;
        }
      }

      .form-group {
        label {
          font-size: 14px;
          margin-bottom: 5px;

          .required {
            color: #f44336;
            font-weight: bold;
          }
        }

        input.form-control {
          margin-bottom: 0;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;

      button {
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;

        &.cancel-button {
          background-color: #f0f0f0;
          border: 1px solid #ccc;

          &:hover {
            background-color: #e0e0e0;
          }
        }

        &.save-button {
          background-color: #3f51b5;
          color: white;
          border: 1px solid #3f51b5;

          &:hover:not(:disabled) {
            background-color: #303f9f;
          }

          &:disabled {
            background-color: #cccccc;
            color: #666666;
            cursor: not-allowed;
            border: 1px solid #cccccc;
          }
        }

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
        }
      }
    }
  }
}

// Specific styles for textarea
.remarks-field {
  textarea.form-control {
    min-height: 80px;
    resize: vertical;
  }
}
