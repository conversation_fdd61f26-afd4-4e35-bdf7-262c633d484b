import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Globals } from 'app/appconfig/appconfig.component';
import { CustomerProfile } from 'app/model/customerprofile.model';
import { Status } from 'app/model/status.model';
import { User } from 'app/model/user.model';
import { FileSaverService } from 'ngx-filesaver';
import { Observable } from 'rxjs';

import { environment } from '../../environments/environment';
import { AgentProfile } from '../model/AgentProfile.model';

@Injectable({
  providedIn: 'root'
})
export class UsersService {

  http: HttpClient;
  //basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals,private fileSaverService: FileSaverService) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  getUsers(): Observable<User[]> {
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<User[]>( `${environment.apiUrl}/api/user/list`, {
        headers:headers
      });
  }

  getUserDetails(): Observable<User> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<User>(`${environment.apiUrl}/api/user/`, {
      headers: headers
    });
  }

  getAgents(): Observable<AgentProfile[]>{
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<AgentProfile[]>( `${environment.apiUrl}/api/user/agents`, {
        headers:headers
      });

  }


  addUser(profile: CustomerProfile): Observable<Status> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/user/`, profile, {
      headers: headers
    });
  }


  downloadCustomers(){
    let headers = new HttpHeaders({'Content-Type': 'application/csv'})
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    this.http.get(`${environment.apiUrl}/api/user/retailer/export`, {
      observe: 'response',
      responseType: 'blob',
      headers:headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, 'customers.csv');
    });
  }

}
