{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20200628.160418.10956.0.001.json", "dumpEventTime": "2020-06-28T16:04:18Z", "dumpEventTimeStamp": "1593340458228", "processId": 10956, "cwd": "D:\\proj\\virtual_ordermgmt_web", "commandLine": ["node", "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\\\node_modules\\@angular\\cli\\bin\\ng", "serve"], "nodejsVersion": "v12.15.0", "wordSize": 64, "arch": "x64", "platform": "win32", "componentVersions": {"node": "12.15.0", "v8": "7.7.299.13-node.16", "uv": "1.33.1", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.40.0", "napi": "5", "llhttp": "2.0.4", "http_parser": "2.9.3", "openssl": "1.1.1d", "cldr": "35.1", "icu": "64.2", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.15.0/node-v12.15.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.15.0/node-v12.15.0.tar.gz", "libUrl": "https://nodejs.org/download/release/v12.15.0/win-x64/node.lib"}, "osName": "Windows_NT", "osRelease": "10.0.19041", "osVersion": "Windows 10 Home Single Language", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 27868421, "nice": 0, "sys": 35284343, "idle": 292850593, "irq": 12275218}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 17024093, "nice": 0, "sys": 10084125, "idle": 328895078, "irq": 1308296}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 36679515, "nice": 0, "sys": 13812859, "idle": 305510890, "irq": 338343}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 22258343, "nice": 0, "sys": 7548703, "idle": 326196234, "irq": 157765}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 29942140, "nice": 0, "sys": 12307921, "idle": 313753250, "irq": 227015}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 18366375, "nice": 0, "sys": 7286937, "idle": 330350000, "irq": 160765}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 28539031, "nice": 0, "sys": 12486296, "idle": 314977968, "irq": 309687}, {"model": "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz", "speed": 2112, "user": 22815890, "nice": 0, "sys": 8562031, "idle": 324625375, "irq": 145281}], "networkInterfaces": [{"name": "VMware Network Adapter VMnet1", "internal": false, "mac": "00:50:56:c0:00:01", "address": "fe80::f982:f2b8:f9df:57a3", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 19}, {"name": "VMware Network Adapter VMnet1", "internal": false, "mac": "00:50:56:c0:00:01", "address": "**************", "netmask": "***********", "family": "IPv4"}, {"name": "VMware Network Adapter VMnet8", "internal": false, "mac": "00:50:56:c0:00:08", "address": "fe80::5076:26dd:7f36:d24f", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 3}, {"name": "VMware Network Adapter VMnet8", "internal": false, "mac": "00:50:56:c0:00:08", "address": "**************", "netmask": "***********", "family": "IPv4"}, {"name": "Wi-Fi", "internal": false, "mac": "c0:e4:34:54:f6:89", "address": "fe80::908:9d9c:ce6:e204", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 12}, {"name": "Wi-Fi", "internal": false, "mac": "c0:e4:34:54:f6:89", "address": "***********", "netmask": "*************", "family": "IPv4"}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}], "host": "<PERSON><PERSON><PERSON><PERSON>"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00007ff7a6f01759", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+10873"}, {"pc": "0x00007ff7a6f05b7c", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+28316"}, {"pc": "0x00007ff7a6f04b38", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+24152"}, {"pc": "0x00007ff7a6ff446b", "symbol": "v8::base::CPU::has_sse+37723"}, {"pc": "0x00007ff7a77f8d9e", "symbol": "v8::Isolate::ReportExternalAllocationLimitReached+94"}, {"pc": "0x00007ff7a77e0de1", "symbol": "v8::SharedArrayBuffer::Externalize+833"}, {"pc": "0x00007ff7a76ae6ac", "symbol": "v8::internal::Heap::EphemeronKeyWriteBarrierFromCode+1436"}, {"pc": "0x00007ff7a76b9a50", "symbol": "v8::internal::Heap::ProtectUnprotectedMemoryChunks+1312"}, {"pc": "0x00007ff7a76b6584", "symbol": "v8::internal::Heap::PageFlagsAreConsistent+3204"}, {"pc": "0x00007ff7a76abe13", "symbol": "v8::internal::Heap::CollectGarbage+1283"}, {"pc": "0x00007ff7a76aa5e4", "symbol": "v8::internal::Heap::AddRetainedMap+2356"}, {"pc": "0x00007ff7a76cb8b5", "symbol": "v8::internal::Factory::NewFillerObject+53"}, {"pc": "0x00007ff7a7437b89", "symbol": "v8::internal::interpreter::JumpTableTargetOffsets::iterator::operator=+4057"}, {"pc": "0x00007ff7a7c24d3d", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+567949"}, {"pc": "0x00007ff7a7c89e1c", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+981868"}, {"pc": "0x00007ff7a7ba48cc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42524"}, {"pc": "0x0000013aeaa398fc", "symbol": ""}], "javascriptHeap": {"totalMemory": 2185261056, "totalCommittedMemory": 2185261056, "usedMemory": 2106329184, "availableMemory": 45196096, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 262144, "capacity": 261872, "used": 32296, "available": 229576}, "new_space": {"memorySize": 33554432, "committedMemory": 33554432, "capacity": 16759808, "used": 1627656, "available": 15132152}, "old_space": {"memorySize": 1886760960, "committedMemory": 1886760960, "capacity": 1864955456, "used": 1864435136, "available": 520320}, "code_space": {"memorySize": 17989632, "committedMemory": 17989632, "capacity": 15397536, "used": 15397536, "available": 0}, "map_space": {"memorySize": 24907776, "committedMemory": 24907776, "capacity": 3936080, "used": 3936080, "available": 0}, "large_object_space": {"memorySize": 221163520, "committedMemory": 221163520, "capacity": 220350080, "used": 220350080, "available": 0}, "code_large_object_space": {"memorySize": 622592, "committedMemory": 622592, "capacity": 550400, "used": 550400, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 16759808, "used": 0, "available": 16759808}}}, "resourceUsage": {"userCpuSeconds": 672.906, "kernelCpuSeconds": 16.546, "cpuConsumptionPercent": 7.80806, "maxRss": 2331045888, "pageFaults": {"IORequired": 2934951, "IONotRequired": 0}, "fsActivity": {"reads": 26896, "writes": 55}}, "libuv": [], "environmentVariables": {"=::": "::\\", "=C:": "C:\\proj", "=D:": "D:\\proj\\virtual_ordermgmt_web", "ALLUSERSPROFILE": "C:\\ProgramData", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "BROWSERSLIST_IGNORE_OLD_DATA": "1", "ChocolateyInstall": "C:\\ProgramData\\chocolatey", "ChocolateyLastPathUpdate": "132346400009518483", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "GODHAVARI", "ComSpec": "C:\\WINDOWS\\system32\\cmd.exe", "dp0": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "ERLANG_HOME": "d:\\Program Files\\erl10.4", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\RAMA KRISHNA", "JAVA_HOME": "d:\\jdk14", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\GODHAVARI", "M2_HOME": "D:\\tools\\maven", "MSMPI_BENCHMARKS": "C:\\Program Files\\Microsoft MPI\\Benchmarks\\", "MSMPI_BIN": "C:\\Program Files\\Microsoft MPI\\Bin\\", "NUMBER_OF_PROCESSORS": "8", "OneDrive": "D:\\OneDrive\\OneDrive - 3Frames Software Lab", "OneDriveCommercial": "D:\\OneDrive\\OneDrive - 3Frames Software Lab", "OnlineServices": "Online Services", "OS": "Windows_NT", "Path": "C:\\Program Files\\Microsoft MPI\\Bin\\;C:\\Python38\\Scripts\\;C:\\Python38\\;D:\\Python\\Python38-32\\Scripts\\;D:\\Python\\Python38-32\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program Files\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;d:\\Program Files\\Git\\cmd;C:\\ProgramData\\chocolatey\\bin;d:\\jdk14\\bin;D:\\Program Files\\erl10.4\\bin;D:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;D:\\Program Files (x86)\\Microsoft SQL Server\\150\\Tools\\Binn\\;D:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;D:\\Program Files\\Microsoft SQL Server\\150\\DTS\\Binn\\;D:\\Program Files (x86)\\Microsoft SQL Server\\150\\DTS\\Binn\\;D:\\tools\\maven\\bin;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\npm", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC;.PY;.PYW", "platformcode": "KV", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 142 Stepping 12, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "8e0c", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules;D:\\Program Files (x86)\\Microsoft SQL Server\\150\\Tools\\PowerShell\\Modules\\", "PUBLIC": "C:\\Users\\<USER>", "RegionCode": "APJ", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "SystemDrive": "C:", "SystemRoot": "C:\\WINDOWS", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "USERDOMAIN": "GODHAVARI", "USERDOMAIN_ROAMINGPROFILE": "GODHAVARI", "USERNAME": "RAMA KRISHNA", "USERPROFILE": "C:\\Users\\<USER>", "WEBPACK_DEV_SERVER": "true", "windir": "C:\\WINDOWS", "_prog": "node"}, "sharedObjects": ["C:\\Program Files\\nodejs\\node.exe", "C:\\WINDOWS\\SYSTEM32\\ntdll.dll", "C:\\WINDOWS\\System32\\KERNEL32.DLL", "C:\\WINDOWS\\System32\\KERNELBASE.dll", "C:\\WINDOWS\\System32\\WS2_32.dll", "C:\\WINDOWS\\System32\\RPCRT4.dll", "C:\\WINDOWS\\SYSTEM32\\dbghelp.dll", "C:\\WINDOWS\\System32\\ADVAPI32.dll", "C:\\WINDOWS\\System32\\ucrtbase.dll", "C:\\WINDOWS\\System32\\msvcrt.dll", "C:\\WINDOWS\\System32\\sechost.dll", "C:\\WINDOWS\\System32\\USER32.dll", "C:\\WINDOWS\\System32\\win32u.dll", "C:\\WINDOWS\\System32\\GDI32.dll", "C:\\WINDOWS\\System32\\gdi32full.dll", "C:\\WINDOWS\\System32\\msvcp_win.dll", "C:\\WINDOWS\\System32\\PSAPI.DLL", "C:\\WINDOWS\\System32\\CRYPT32.dll", "C:\\WINDOWS\\System32\\bcrypt.dll", "C:\\WINDOWS\\SYSTEM32\\IPHLPAPI.DLL", "C:\\WINDOWS\\SYSTEM32\\USERENV.dll", "C:\\WINDOWS\\SYSTEM32\\WINMM.dll", "C:\\WINDOWS\\System32\\IMM32.DLL", "C:\\WINDOWS\\SYSTEM32\\powrprof.dll", "C:\\WINDOWS\\SYSTEM32\\UMPDC.dll", "C:\\WINDOWS\\SYSTEM32\\CRYPTBASE.DLL", "C:\\WINDOWS\\system32\\uxtheme.dll", "C:\\WINDOWS\\System32\\combase.dll", "C:\\WINDOWS\\system32\\mswsock.dll", "C:\\WINDOWS\\SYSTEM32\\kernel.appcore.dll", "C:\\WINDOWS\\System32\\bcryptprimitives.dll", "C:\\WINDOWS\\System32\\NSI.dll", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc6.DLL", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc.DLL", "C:\\WINDOWS\\SYSTEM32\\DNSAPI.dll", "C:\\WINDOWS\\system32\\napinsp.dll", "C:\\WINDOWS\\system32\\pnrpnsp.dll", "C:\\WINDOWS\\system32\\wshbth.dll", "C:\\WINDOWS\\system32\\NLAapi.dll", "C:\\WINDOWS\\System32\\winrnr.dll", "C:\\Windows\\System32\\rasadhlp.dll", "C:\\WINDOWS\\System32\\fwpuclnt.dll"]}