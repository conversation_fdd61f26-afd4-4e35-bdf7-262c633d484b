<!-- <div mat-dialog-content>{{confirmMessage}}</div> -->
<!-- <div mat-dialog-actions class="pt-24">
    <button mat-raised-button class="mat-accent mr-16" (click)="dialogRef.close(true)">Confirm</button>
    <button mat-button (click)="dialogRef.close(false)">Cancel</button>
</div> -->



<!-- <table class="table table-bordered">
        <thead>
            <tr>
                <th *ngFor="let code of headElements" scope="col">{{code}}</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let ratio of sizeRatios; let i = index">
                <th  width="100px;" scope="row">{{ratio}}</th>
                <td  width="50px;" *ngFor="let code of getEntities(ratio); let j = index"><input type="text"  [(ngModel)]="code.qty" id="{{code}}{{j}}" value="" /></td>
            </tr>
        </tbody>


    </table> -->

<table mdbTable bordered="true">
  <thead>
    <tr>
      <th width="10%">Ratio</th>
      <th *ngFor="let head of sizeCodes" scope="col">{{head}} </th>
    </tr>
  </thead>
  <tbody>
    <tr mdbTableCol *ngFor="let ratio of sizeRatios; let i = index">
      <th scope="row">{{ratio}}</th>
      <td *ngFor="let code of getEntities(ratio); let j = index"><input
          style="border: 1px solid black;border-radius:4px;width:45px" type="text"  maxlength="4"
          pattern="^[1-9][0-9]*$"
          oninput="if(!this.value.match('^[1-9][0-9]*$'))this.value='';" [(ngModel)]="code.qty" id="{{ratio}}{{j}}"></td>
    </tr>
  </tbody>
</table>

<button type="button" (click)="save()" class="btn btn-primary btnsd">Save</button>
