import { Component, Optional, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Title } from '@angular/platform-browser';
import { environment } from '../environments/environment';
import { Router, NavigationEnd } from '@angular/router';

declare let gtag: Function;

//import * as config from './assets/config.json';

@Component({
	selector: 'chankya-app',
	template: '<router-outlet></router-outlet>',
	encapsulation: ViewEncapsulation.None
})

export class ChankyaAppComponent {
	constructor(translate: TranslateService, private titleService: Title, public router: Router) {
		this.router.events.subscribe(event => {
			if (event instanceof NavigationEnd) {
				// gtag('config', 'G-E0N8P3KDYQ',
				// 	{
				// 		'page_path': event.urlAfterRedirects
				// 	}
				// );
			}
		});
		translate.addLangs(['en', 'fr', 'he', 'ru', 'ar', 'zh', 'de', 'es', 'ja', 'ko', 'it', 'hu']);
		translate.setDefaultLang('en');
		//let title = (config as any).title;
		this.titleService.setTitle(`${environment.title}`);

		const browserLang: string = translate.getBrowserLang();
		translate.use(browserLang.match(/en|fr/) ? browserLang : 'en');
	}
}
