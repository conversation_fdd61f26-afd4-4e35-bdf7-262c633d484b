import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { Drop } from 'app/model/Drop.model';
import { User } from 'app/model/user.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DropsService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  public getAllDeliveries(): Observable<Drop[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Drop[]>( Appconfig.BASE_URL+'api/techpack/delivery/all', {
        headers:headers
      });
}


createDrop(drop: Drop): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');
  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.post<Response>(Appconfig.BASE_URL + 'api/techpack/delivery/', drop, {
    headers: headers
  });
}

updateDrop(drop: Drop): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/delivery/', drop, {
    headers: headers
  });
}

deactivateDrop(deliveryUid: string): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/delivery/' + deliveryUid, {
    headers: headers
  });
}

activateDrop(drop: Drop): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
  .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/delivery/'+ drop.uuid +'/activate', drop, {
    headers: headers
  });
}
}
