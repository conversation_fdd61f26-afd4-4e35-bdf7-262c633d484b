<div class="dialog-header">
  <h2 mat-dialog-title>{{ data.title }}</h2>
  <button type="button" class="close-button" (click)="onNoClick()">
    <span class="close-icon">×</span>
  </button>
</div>
<div mat-dialog-content>
  <p>{{ data.message }}</p>

   <!-- Remarks text area field -->
  <div class="remarks-field">
    <label for="remarks">Remarks:</label>
    <textarea id="remarks" [(ngModel)]="remarks" class="form-control" rows="3" placeholder="Enter Remarks"></textarea>
  </div>

  <!-- Expiry Date field -->
  <div class="expiry-date-field">
    <label for="expiryDate">Expiry Date:</label>
    <input type="date" id="expiryDate" [(ngModel)]="expiryDate" class="form-control" placeholder="DD-MM-YYYY" />
  </div>

  <!-- Ship To dropdown field -->
  <div *ngIf="data.showShipToField && showShipTo" class="ship-to-field">
    <label for="shipTo">Ship To Shop Name:</label> <!-- Changed label to "Ship To Shop Name" -->

    <!-- Loading state -->
    <div *ngIf="isLoadingShipToOptions" class="loading-message">
      Loading shipping options...
    </div>

    <!-- Error state -->
    <div *ngIf="shipToLoadError" class="error-message">
      {{ shipToLoadError }}
      <button type="button" class="retry-button" (click)="loadShipToOptions()">Retry</button>
    </div>

    <!-- Ship to dropdown and add new button -->
    <div *ngIf="!isLoadingShipToOptions && !shipToLoadError && showShipTo" class="ship-to-container">
      <select *ngIf="!showNewShipToForm" id="shipTo" [(ngModel)]="selectedShipTo" class="form-control">
        <option *ngFor="let option of data.shipToOptions" [ngValue]="option">
          {{ option.shipPartyName }}
        </option>
      </select>
      <button *ngIf="!showNewShipToForm" type="button" class="add-new-button" (click)="toggleNewShipToForm()">
        <span>+ Add New</span>
      </button>
    </div>



    <!-- New Ship To Form -->
    <div *ngIf="showNewShipToForm && !isLoadingShipToOptions && !shipToLoadError && showShipTo" class="new-ship-to-form">
      <!-- Error message for ship-to creation -->
      <div *ngIf="shipToCreateError" class="create-error-message">
        {{ shipToCreateError }}
        <button type="button" class="close-error-button" (click)="shipToCreateError = ''">×</button>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="newShipToAddress">Ship To Address: <span class="required">*</span></label>
          <input type="text" id="newShipToAddress" [(ngModel)]="newShipTo.address1" class="form-control" placeholder="Enter Shipping Address" required>
        </div>
      </div>



      <div class="form-row">
        <div class="form-group">
          <label for="newShipToId">Ship To ID: <span class="required">*</span></label>
          <input type="text" id="newShipToId" [(ngModel)]="newShipTo.shipToId" class="form-control" placeholder="Enter Ship To ID" required>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="cancel-button" (click)="toggleNewShipToForm()" [disabled]="isSavingNewShipTo">Cancel</button>
        <button type="button" class="save-button" (click)="saveNewShipTo()"
                [disabled]="!newShipTo.address1 || !newShipTo.shipToId || isSavingNewShipTo">
          <span *ngIf="!isSavingNewShipTo">Save Address</span>
          <span *ngIf="isSavingNewShipTo">Saving...</span>
        </button>
      </div>
    </div>

  </div>




</div>
<div mat-dialog-actions>
  <button mat-button (click)="onNoClick()">{{ data.cancelText || 'Cancel' }}</button>
  <button mat-button color="primary" (click)="onYesClick()" cdkFocusInitial>{{ data.confirmText || 'Confirm' }}</button>
</div>
