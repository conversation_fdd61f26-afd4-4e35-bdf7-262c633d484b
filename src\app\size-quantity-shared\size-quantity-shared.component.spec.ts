import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SizeQuantitySharedComponent } from './size-quantity-shared.component';

describe('SizeQuantitySharedComponent', () => {
  let component: SizeQuantitySharedComponent;
  let fixture: ComponentFixture<SizeQuantitySharedComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SizeQuantitySharedComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SizeQuantitySharedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
