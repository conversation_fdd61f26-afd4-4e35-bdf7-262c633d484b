.search-results {
  overflow: hidden;
}

.container-fluid {
  font-family: Roboto !important;
}

.stickyListHeader {
  position: sticky;
  top: -1px;
  z-index: 9999;
  background-color: #fff;
  padding: 8px;
  border-bottom: 1px solid lightgray;
}

.filterWithoutScroll {
  position: relative;
  height: 48px;
}

.stickyFilter {
  display: block;
  // position: absolute;
  background-color: white;
  width: 100%;
  left: 0;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 9999999;
}

.filterButton:hover .stickyFilter {
  display: block;
}

.filterText {
  font-family: Roboto;
  margin-left: 5px;
  cursor: pointer;
}

.sz {
  width: 100px;
}

.product-price {
  font-size: 14px;
  color: #ff905a;
  font-weight: 500;
}

.no-products {
  color: red;
  font-size: 19px;
  text-align: center;
}

.product-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  color: #282c3f;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// .gallery-wrapper {
//     a.expand {
//         display: block;
//         position: absolute;
//         left: 0;
//         right: 0;
//         margin: 0 auto;
//         z-index: 100;
//         width: 60px;
//         height: 60px;
//         border: 4px solid white;
//         text-align: center;
//         color: white;
//         line-height: 50px;
//         font-size: 30px;
//         border-radius: 30px;
//     }
// }

// .gallery-wrapper {
//     .overlay-wrap {
//         overflow: hidden;
//         transition: all 1s linear 0s;
//         &:hover {
//             img {
//                 transform: scale(1.2);
//             }
//         }
//         img {
//             transform: scale(1);
//             transition: all 1s linear 0s;
//         }
//     }
// }

// .gallery-wrapper {
//     margin-top: 50px;
// }

// .search-results {
//     height: 100%;
//     overflow: scroll;
// }

// .gallery-item:nth-child(even),
// .gallery-item:nth-child(odd) {
//     background-color: #fff !important;
// }

// .gallery-item:nth-child(even) {
//     padding: 0px;
//     background-color: #c5bdbd;
// }
// .gallery-item:nth-child(odd) {
//     padding: 0px;
//     background-color: #e8e2e2;
// }


@media screen and (max-width:600px) {
  .glry {
    width: 100% !important;
  }

  .imgsmallscrwth {
    width: 100% !important;
  }

  .gridicon {
    margin-top: 20px;
  }


}

.plr-40 {
  padding: 0px 40px !important;
}

.imgsmallscrwth {
  margin-top: 10px;
}

.picsum-img-wrapper {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.overCard {
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.showIconsOnhover {
  visibility: hidden;
  display: flex;
  flex-direction: row;
}

.overCard:hover {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;

  .showIconsOnhover {
    visibility: visible;
    display: flex;
    flex-direction: row;
  }
}

.isProductTitleCenter {
  display: flex;
  align-items: center;
  width: 100%;
}

.each_corousel {
  margin-bottom: 20px;
}

.product-description {
  color: #535766;
  font-size: 14px;
  line-height: 1;
  margin-bottom: 0;
  margin-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
  display: block;
  text-transform: capitalize;
}

.eachItemmargin {
  margin: 5px;
}

.new-badge { 
  max-width:80px;
  max-height:100px;
  position: absolute;
  top: 10px; 
  left: 10px; 
}

.background-image {
  display: block;
  height: 100%;
  width: 100%;
}

.picsum-img-wrapper {
  position: relative;
}

