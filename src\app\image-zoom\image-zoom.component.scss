.images {
    position: relative;
    //width:50%;
    overflow: hidden;
}

/* Point-zoom Container */
// .images img {
//     transform-origin: 65% 75%;
//     transition: transform 1s, filter 0.5s ease-out;
// }

/* The Transformation */
// .images:hover img {
//     transform: scale(5);
// }
// .images:hover img {
//     filter: grayscale(0);
//     transform: scale(2);
// }
.zoom-image-thumbnails-wrapper {
    margin-right: 10px;
    width: 100x;
    height: 100%;
}
.diplaying-image {
    width: 100%;
    height: 100%;
}

.eachImage-thumbnail {
    border: 2px solid red;
    margin: 1px;
}
.activeDisplayingImage {
    border: 2px solid green;
}
.mb-5,
.my-5 {
    margin-bottom: 0rem !important;
}

.mt-5,
.my-5 {
    margin-top: 2rem !important;
}

.image-count {
    position: absolute;
    color: red;
    //top: 10px;
    right: 0;
}

.purchased-order-view-button{
    font-size: 18px; 
    color: black; 
    border: 1px solid;
}
::ng-deep img.ngxImageZoomThumbnail{
    height: 500px !important;
}

.images_wrapper{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    width: 45%;
    margin: 5px 20px;
}
.ngxImageZoomContainer {
    position: relative;
    margin: auto;
    overflow: hidden;
    pointer-events: none;
    width: 100% !important;
}

.new-badge{
    max-width:80px;
    max-height:100px;
    position: absolute;
    top: 10px; 
    left: 10px; 
}