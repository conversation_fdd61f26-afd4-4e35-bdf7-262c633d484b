{"compileOnSave": true, "compilerOptions": {"outDir": "./dist/out-tsc", "baseUrl": "src", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "module": "esnext", "importHelpers": true, "target": "es5", "resolveJsonModule": true, "esModuleInterop": true, "typeRoots": ["node_modules/@types"], "types": ["node"], "lib": ["es2016", "es2015", "es2017", "dom"]}}