import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Catalog } from 'app/model/catalog.model';
// import { MDCSlider } from '@material/slider';

import { Category } from 'app/model/Category.model';
import { OrderItem } from 'app/model/orderitem.model';
import { SizeRatio } from 'app/model/SizeRatio.model';
import { User } from 'app/model/user.model';
import { CatalogService } from 'app/service/catalog.service';
import { CategoryService } from 'app/service/category.service';
import { OrderService } from 'app/service/order.service';
import { SizeratioService } from 'app/service/sizeratio.service';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subscription } from 'rxjs';

@Component({
  selector: 'app-addcart-details',
  templateUrl: './addcart-details.component.html',
  styleUrls: ['./addcart-details.component.scss']
})
export class AddcartDetailsComponent implements OnInit, OnDestroy {
  product: Catalog = new Catalog();
  @Input() productDetails: Observable<any>
  @Input() readonly: boolean = false
  @Input() isDialog: boolean = false
  user: User = new User();
  category: Category = new Category();
  sizeGradeForm: FormGroup;
  subscription: Subscription = new Subscription();
  qtyData: any[] = [];
  sizeCodes: string[] = [];
  sizeRatios: string[] = ["A", "B", "C", "D"];
  sizeRatioData: SizeRatio[] = [];
  sizeCodeIndexMap = new Map();
  orderItemData: OrderItem[] = [];
  sizeRatio: string = "";
  comment: string = "";
  rating: string = "";
  validRatings: any[] = [];
  dropdownRating: string = "";
  disabledSizeQuantity: boolean = false;
  unitFlag: boolean = false;
  sizeCodeUnit = [];
  sizeunitArr = []
  stockCheckdata = [];
  stockCheckSum = [];
  isRetailer: boolean = false;
  catalogPrices: any[] = [];
  isWishList: boolean;
  isCart: boolean;
  isDataLoading: boolean = false;
  totalQuantity: number = 0;
  totalUnits: number = 0;
  totalPrice: number = 0;
  netPrice: number;
  selectedSizeUnit: any = 'Pc';
  totalMRP: number = 0;

  tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"))

  @Output() sizeQuantityData: EventEmitter<any> = new EventEmitter();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private orderService: OrderService,
    private toastrService: ToastrService,
    private catalogService: CatalogService,
    private categoryService: CategoryService,
    private _SizeratioService: SizeratioService,
    // public dialogRef: MatDialogRef<SizeQuantitySharedComponent>,
    private _changeDetectorRef: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    // const slider = new MDCSlider(document.querySelector('.mdc-slider'));
    this.isWishList = this.router.url.includes('wishlistGrid') ? true : false;
    this.isCart = this.router.url.includes('cart') ? true : false;
    this.unitFlag = true;
    this.user = JSON.parse(localStorage.getItem("userProfile"));
    if (this.user.roles.includes('RETAILER')) {
      this.isRetailer = true;
    }

    let tenantData = JSON.parse(localStorage.getItem("tenantConfig"));
    let tenantnewData = tenantData;

    //this.getCategory();
    let sizeunitStr: any;
    if (tenantnewData.sizeCodeUnitMap) {
      sizeunitStr = JSON.parse(tenantnewData.sizeCodeUnitMap);
    } else {

    }

    for (let key in sizeunitStr) {

      this.sizeCodeUnit.push(key);

    }
    this.sizeunitArr = this.sizeCodeUnit;
    if (this.productDetails != null) {
      this.productDetails
        .subscribe(res => {
          if (res) {
            this.resetAllCValues();
            this.product = res;
            this.catalogPrices = [];
            let prices: any = this.product.prices;
            for (let key in prices) {
              if (prices.hasOwnProperty(key)) {
                this.catalogPrices.push(prices[key])
              }
            }
            if (this.product.uuid) {
              this.getSizeRatio();
            }
            if (res.stockCheck == 0 && !res.catalogPriceSizeCodes) {
              this.getCategory();
            }
            else if (res && res.catalogPriceSizeCodes || res.availableSizeCodes) {
              if (res.stockCheck == 0 && res.catalogPriceSizeCodes != null) {
                this.getCatalogPriceSizeCodes(res);
              }
              if (res.stockCheck == 1 && res.warehouseStockQty > 0 && res.availableSizeCodes != null) {
                this.getAvailableSizeCodes(res);
              }
            }
          }
        })
    } else {
      if (this.product.uuid) {
        this.catalogPrices = [];
        let prices: any = this.product.prices;
        for (let key in prices) {
          if (prices.hasOwnProperty(key)) {
            this.catalogPrices.push(prices[key])
          }
        }
        this.getSizeRatio();
        if (this.product.stockCheck == 0 && this.product.catalogPriceSizeCodes == null) {
          this.getCategory();
        }
        else if (this.product.stockCheck == 0 && this.product.catalogPriceSizeCodes !== null) {
          this.getCatalogPriceSizeCodes(this.product);
        }
        else if (this.product.stockCheck == 1) {
          if (this.product.stockCheck == 1 && this.product.warehouseStockQty > 0 && this.product.availableSizeCodes != null) {
            this.getAvailableSizeCodes(this.product);
          }
        }
      }
    }
    this.user = JSON.parse(localStorage.getItem("userProfile"));
    let ratings: string = this.user.validRatings;
    if (ratings != null && ratings != undefined && ratings.trim().length == 0) {
      this.validRatings = null
    }
    if (this.validRatings != null)
      this.validRatings = ratings.split(',');
    // console.log("this.validRatings ",this.validRatings )


  }

  // ngOnChanges() {
  //   if (this.product.uuid) {
  //     this.getCategory();
  //     this.getSizeRatio();
  //   }
  // }

  getProductStock() {
    this.catalogService.getProductStock(this.product).subscribe(data => {
      this.stockCheckdata = data;
      this.initStockData();

    });
  }

  checkQuantity(code) {
    if (this.stockCheckSum.length > 0) {
      let result = this.stockCheckSum.find(obj => {
        return obj.sizecode === code.sizecode
      })
      return result.qty <= 0;
    } else {
      return false;
    }

  }

  getCategory() {
    this.categoryService.getByCode(this.product.subCatUid).subscribe(data => {
      if (data.code == "STSHT") {
        this.unitFlag = true
      }
      else {
        this.unitFlag = false;
      }
      this.category = data;
      this.sizeCodes = [];
      let j = this.category.sizeCode ? this.category.sizeCode.length : 0;

      let i = 0;
      let k = 0;
      while (i < j) {
        let code = this.category.sizeCode.substring(i, i + 3);
        this.sizeCodes.push(code);
        i += 3;
        this.sizeCodeIndexMap.set(code, k++);
      }
      this.initStockData();
      this.getOrderItems();
    });
  }
  getSetCategory() {
    this.categoryService.getByCode(this.product.subCatUid).subscribe(data => {

      this.category = data;

      this.sizeCodes = [];
      let j = this.category.sizeCode2.length;
      let i = 0;
      let k = 0;
      while (i < j) {
        let code = this.category.sizeCode2.substring(i, i + 3);
        this.sizeCodes.push(code);
        i += 3;
        this.sizeCodeIndexMap.set(code, k++);
      }
      this.initStockData();
      this.getOrderItems();
    });


  }

  public getCatalogPriceSizeCodes(res: any): void {
    if (res && res.catalogPriceSizeCodes) {
      this.sizeCodes = [];
      if (res.stockCheck == 0 && res.catalogPriceSizeCodes != null) {
        let j = res.catalogPriceSizeCodes ? res.catalogPriceSizeCodes.replace(/,/g, "").length : 0;
        let i = 0;
        let k = 0;
        this.sizeCodes = [];
        while (i < j) {
          let code: string = res.catalogPriceSizeCodes.toString().replace(/,/g, "").substring(i, i + 3);
          this.sizeCodes.push(code.replace(' ', ""));
          i += 3;
          this.sizeCodeIndexMap.set(code, k++);
        }
      }
    }
    this.getOrderItems();
    this.initStockData();
    this.initData();
  }

  public getAvailableSizeCodes(res: any): void {
    if (res && res.availableSizeCodes) {
      if (res.stockCheck == 1 && res.warehouseStockQty > 0) {
        let j = res.availableSizeCodes ? res.availableSizeCodes.replace(/,/g, "").length : 0;
        let i = 0;
        let k = 0;
        this.sizeCodes = [];
        while (i < j) {
          let code: string = res.availableSizeCodes.toString().replace(/,/g, "").substring(i, i + 3);
          this.sizeCodes.push(code);
          i += 3;
          this.sizeCodeIndexMap.set(code, k++);
        }
        this.getOrderItems();
        this.initStockData();
        this.initData();
      }
    } else if (res.stockCheck && res.stockCheck == 0) {
      if (res.catalogPriceSizeCodes && res.catalogPriceSizeCodes != null) {
        this.getCatalogPriceSizeCodes(res);
      } else {
        this.getCategory();
      }
    }
  }


  loadForm(event) {
    if (this.selectedSizeUnit === 'Pc') {
      this.getCategory()
    }
    if (this.selectedSizeUnit === 'Set') {
      this.getSetCategory()
    }
  }


  getOrderItems() {
    this.orderService.getOrderItemsByCatalogUid(this.product.uuid, 1).subscribe(data => {
      this.orderItemData = data;
      if (data.length > 0) {
        var item = data[0];

        this.comment = item.comment
        this.rating = item.rating
        this.dropdownRating = item.rating
        this.sizeRatio = item.sizeRatio;
      }
      this.initData();

    });
  }

  getSizeRatio() {
    this.orderService.getSizeRatioBySubcat(this.product.subCatUid).subscribe(data => {
      this.sizeRatioData = data;
      if (parseInt(this.product.stockCheck.toString()) == 1) {
        // console.log("enter loop")
        this.getProductStock();
      } else {

      }

    });
  }

  initData() {
    this.isDataLoading = true;
    this.qtyData = []
    let data = []
    for (let i in this.sizeCodes) {
      let obj = {
        'sizecode': this.sizeCodes[i],
        'qty': this.getQty(this.sizeCodes[i]),
        'price': this.getPricesAndMulitpler(this.sizeCodes[i], 'price'),
        'multiplier': this.getPricesAndMulitpler(this.sizeCodes[i], 'multiplier'),
        'unitUid': this.getPricesAndMulitpler(this.sizeCodes[i], 'unitUid')
      };
      data.push(obj)
    }
    this.qtyData = [...data];
    this.isDataLoading = false;
    this._changeDetectorRef.detectChanges();
  }

  getQty(code: string) {
    for (let i in this.orderItemData) {
      if (this.orderItemData[i].sizeCode == code) {
        return this.orderItemData[i].qty
      }
    }
    return '';
  }

  initStockData() {
    this.stockCheckSum = [];
    let data = [];
    for (let i in this.sizeCodes) {
      let obj = { 'sizecode': this.sizeCodes[i], 'qty': this.getStockQty(this.sizeCodes[i]), 'price': this.getPricesAndMulitpler(this.sizeCodes[i], 'price'), 'multiplier': this.getPricesAndMulitpler(this.sizeCodes[i], 'multiplier'), 'unitUid': this.getPricesAndMulitpler(this.sizeCodes[i], 'unitUid') };
      data.push(obj);
    }
    this.stockCheckSum = data;
    // console.log("stock s",this.stockCheckSum)
    this._changeDetectorRef.detectChanges();
  }

  getStockQty(code: string) {
    // let size = parseInt(code);
    let sum = 0;
    if (this.stockCheckdata[code]) {
      this.stockCheckdata[code].forEach(element => {
        sum = sum + element.qty
      });
      return sum
    } else {
      return;
    }

  }

  getPricesAndMulitpler(code: string, status: string) {
    let value: any;
    if (this.catalogPrices && this.catalogPrices.length > 0) {
      this.catalogPrices.forEach(item => {
        if (item.sizeCode.trim() == code.trim()) {
          if (status == 'price') {
            value = item.price;
          } else if (status == 'multiplier') {
            value = item.multiplier;
          }
          else {
            value = item.unitUid;
          }
        }
      })
    }
    return value ? value : null
  }


  resetSizeRatioField(event) {
    this.sizeRatio = '';
  }

  populateSizeRatio(event: any) {
    let isValid = false;
    if (this.sizeRatio == '' || this.sizeRatio.trim().length == 0) {
      return;
    }
    this.sizeRatio = this.sizeRatio.toUpperCase();
    for (let i in this.sizeRatioData) {
      if (this.sizeRatioData[i].sizeRatioCode == this.sizeRatio) {
        isValid = true
        if (this.sizeCodeIndexMap.has(this.sizeRatioData[i].sizeCode)) {

          // this.qtyData[this.sizeCodeIndexMap.get(this.sizeRatioDaxta[i].sizeCode)].qty = this.sizeRatioData[i].qty;
          if (this.product.stockCheck && parseInt(this.product.stockCheck.toString()) == 1) {

            if (this.stockCheckSum.length > 0) {

              let index = this.sizeCodeIndexMap.get(this.sizeRatioData[i].sizeCode);
              if (this.stockCheckSum[index].qty >= this.sizeRatioData[i].qty) {
                this.qtyData[index].qty = this.sizeRatioData[i].qty;
              } else {
                if (this.stockCheckSum[index] != undefined && this.stockCheckSum[index].qty != 0 && this.stockCheckSum[index].qty != undefined ) {
                  this.qtyData[i].qty = '';
                  this.toastrService.warning(`Maximum stock quantity available is ${this.stockCheckSum[index].qty}. please enter the quantity less than or equal to stock quantity`);
                }
              }
            }
          } else {
            let index = this.sizeCodeIndexMap.get(this.sizeRatioData[i].sizeCode);
            this.qtyData[index].qty = this.sizeRatioData[i].qty;
          }
          //Focus to last element input
          // let id = this.sizeCodes[i];
          // if (document.getElementById(id)) {
          //   document.getElementById(id).focus();
          // }
        } else {
          console.log("enter loop 1")
          if (this.sizeCodeIndexMap.get(this.sizeRatioData[i].sizeCode) != undefined)
            this.qtyData[this.sizeCodeIndexMap.get(this.sizeRatioData[i].sizeCode)].qty = '';
        }
      }
    }
    if (!isValid) {
      this.sizeRatio = '';
      this.toastrService.error('', 'Invalid size ratio');
    } else {
      this.constructQuatityOrRating('qty', false);
    }

  }

  inputUpdate() {
    if (this.isWishList) {
      this.updateData('local', false);
    } else {
      this.updateData('qty', true);
    }
  }

  // updateRating(status) {
  //   // let regExp = new RegExp("^([a-zA-Z][a-zA-Z ]+)$")
  //   let regex = /^[a-zA-Z]\d{a,z}$/g
  //   console.log("rating", this.rating, regex.test(this.rating))
  //   if (/[a-zA-Z][a-zA-Z ]+/.test(this.rating)) {
  //     this.updateData(status);
  //   } else {
  //     this.rating = 'updateData';
  //     console.log("afterregex",this.rating);
  //     this._changeDetectorRef.detectChanges()
  //   }
  // }

  public updateData(status, isSubmit): void {
    // console.log(":status",status);
    var items = [];
    if (status == 'rating' || status == 'qty') {
      this.constructQuatityOrRating(status, isSubmit);
    }
    else if (status == 'local') {
      this.disabledSizeQuantity = false;
      if (this.isDialog == true) {
        return;
      }
    } else {
      this.disabledSizeQuantity = false;
      let sizeQuanityIsExist: any[] = this.qtyData.filter(item => { if (item.qty) { return item } });
      if (this.rating || sizeQuanityIsExist.length > 0) {
        this.constructQuatityOrRating(status, isSubmit);
      } else {
        this.qtyData = [];
        this.initData();

        items = [];
        let obj = { 'items': items, 'stockchecksum': this.stockCheckSum };
        this.sizeQuantityData.emit(obj);
        this.toastrService.error('Please add rating or size quantity');
      }
    }
  }

  constructQuatityOrRating(status, isSubmit): void {
    var items = [];
    this.totalQuantity;
    this.totalUnits;
    if (status == 'rating') {
      let sizeQuanityIsExist: any[] = this.qtyData.filter(item => { if (item.qty) { return item } });
      if (sizeQuanityIsExist.length > 0 || this.dropdownRating?.length > 0) {
        for (let i in this.qtyData) {
          console.log("this.qtyData",this.qtyData[i])
          // if (this.qtyData[i].qty > 0) {
          var item = new OrderItem();
          item.qty = this.qtyData[i].qty != '' ?parseInt(this.qtyData[i].qty):0;
          item.catalogUid = this.product.uuid;
          item.sizeCode = this.sizeCodes[i];
          item.catUid = this.product.catUid;
          item.subCatUid = this.product.subCatUid;
          item.customerUid = this.user.customerUid;
          this.dropdownRating ? item.rating = this.dropdownRating : item.rating = this.rating;
          item.comment = this.comment;
          item.price = this.qtyData[i].price;
          item.netPrice = this.product.netPrice;
          item.offerMonth = this.product.offerMonth;
          item.offerMonthUid = this.product.offerMonthUid;
          item.imageUid = this.product.imageUid;
          item.shortCode = this.product.shortCode;
          item.sizeRatio = this.sizeRatio
          item.sizeRatioUid = this.sizeRatio,
            item.unitUid = this.getPricesAndMulitpler(this.sizeCodes[i], 'unitUid')
          item.drop = 0;
          if (this.stockCheckSum.length > 0 && parseInt(this.qtyData[i].qty) > this.stockCheckSum[i].qty) {
            this.qtyData[i].qty = '';
            this.toastrService.warning(`Maximum stock quantity available is ${this.stockCheckSum[i].qty}. please enter the quantity less than or equal to stock quantity`);
            // this.qtyData[i].qty = '';
            this.qtyData = [...this.qtyData];
            this.calculateMrpAndQuantiy();
            this._changeDetectorRef.detectChanges();
            continue;
          }
          items.push(item);
        }
        let obj = { 'items': items, 'stockchecksum': this.stockCheckSum, message: '' ,status:'rating'};
        this.sizeQuantityData.emit(obj);
        this._changeDetectorRef.detectChanges();

        // this._changeDetectorRef.detectChanges();
        if (!this.isWishList) {
          // if (isSubmit) {
          //   this.sizeQuantityData.emit(obj);
          // }
        }
      }
      else {

        this.disabledSizeQuantity = false;
        let obj = { 'items': [], 'stockchecksum': this.stockCheckSum, message: '' ,status:''};
        // this.sizeQuantityData.emit(obj);
        this.toastrService.error('Please add rating or size quantity');
      }
      this.calculateMrpAndQuantiy();
    }
    else if (status == 'qty') {
      items = [];
      this.disabledSizeQuantity = false;
      let sizeQuanityIsExist: any[] = this.qtyData.filter(item => { if (item.qty) { return item } });
      // console.log("sizeQuanityIsExist",sizeQuanityIsExist)
      let emitMessage = '';
      if (sizeQuanityIsExist.length > 0) {
        for (let i in this.qtyData) {
          if (this.qtyData[i].qty != '') {
            var item = new OrderItem();
            item.qty = parseInt(this.qtyData[i].qty);
            item.catalogUid = this.product.uuid;
            item.sizeCode = this.sizeCodes[i];
            item.catUid = this.product.catUid;
            item.subCatUid = this.product.subCatUid;
            item.customerUid = this.user.customerUid;
            this.dropdownRating ? item.rating = this.dropdownRating : item.rating = this.rating;
            item.comment = this.comment;
            item.price = this.qtyData[i].price;;
            item.netPrice = this.product.netPrice;
            item.offerMonth = this.product.offerMonth;
            item.offerMonthUid = this.product.offerMonthUid;
            item.imageUid = this.product.imageUid;
            item.shortCode = this.product.shortCode;
            item.sizeRatio = this.sizeRatio
            item.sizeRatioUid = this.sizeRatio
            // console.log("this.size ratio",item.sizeRatio);
            // console.log("this.size code",item.sizeCode )
            item.unitUid = this.getPricesAndMulitpler(this.sizeCodes[i], 'unitUid')
            item.drop = 0;
            items.push(item);
            // console.log("parseInt(this.qtyData[i].qty) > this.stockCheckSum[i].qty",parseInt(this.qtyData[i].qty) > this.stockCheckSum[i].qty)
            if (this.stockCheckSum.length > 0 && parseInt(this.qtyData[i].qty) > this.stockCheckSum[i].qty) {
              emitMessage = `Maximum stock quantity available is ${this.stockCheckSum[i].qty}. Please enter a quantity less than or equal to stock quantity`;
              this.toastrService.warning(emitMessage);
            
              let obj = { 'items': items, 'stockchecksum': this.stockCheckSum, message: emitMessage ,status:''};
              this.sizeQuantityData.emit(obj);
            
              // setTimeout(() => {
                // this.qtyData[i].qty = '';
                this.qtyData = [...this.qtyData];
                this._changeDetectorRef.detectChanges();
                this.calculateMrpAndQuantiy();
              // });
            
              return;
            }

          }
        }
        let obj = { 'items': items, 'stockchecksum': this.stockCheckSum, message: emitMessage,status:'' };
        this.sizeQuantityData.emit(obj);
      } else {
        this.disabledSizeQuantity = false;
        let obj = { 'items': [], 'stockchecksum': this.stockCheckSum, 'removeItemCatuid': this.product.uuid };
        // if (this.isCart) {
        //   this.sizeQuantityData.emit(obj);
        // } else {
        this.toastrService.error('Please add rating or size quantity');
        // }

      }
      this.calculateMrpAndQuantiy();
    }

  }

  calculateMrpAndQuantiy() {
    this.totalMRP = 0;
    this.totalQuantity = 0;
    this.netPrice = 0;
    this.totalUnits = 0;
    this.qtyData.forEach(item => {
      if (item.qty) {

        this.totalMRP = this.totalMRP + ((parseInt(item.qty)) * (this.product.price));
        this.netPrice = this.netPrice + ((parseInt(item.qty)) * (this.product.netPrice));
        this.totalUnits = this.totalUnits + (parseInt(item.qty));
        // console.log("this.qtyData", this.totalUnits)

        this.totalQuantity = this.totalQuantity + (parseInt(item.qty) * this.checkMultiplier(item));
      }
    })
  }
  checkMultiplier(item) {
    if (item.multiplier) {
      return item.multiplier
    } else {
      return 1
    }
  }

  update() {
    this.isDialog = false;
    this.updateData('qty', true);
    // this.close();
  }

  public dropdownRatingChange(): void {
    if (this.dropdownRating) {
      this.constructQuatityOrRating('rating', true);
    } else {
      this.qtyData = [];
      this.initData();

      this.disabledSizeQuantity = false;
    }

  }

  public showIfStockExist(code): boolean {
    let value: boolean = false;
    if (parseInt(this.product.stockCheck.toString()) == 1) {
      let data: any[] = this.stockCheckSum.filter(item => item.sizecode == code.sizecode);
      if (data && data.length > 0) {
        data[0].qty ? value = true : value = false
      }
    } else {
      value = true;
    }
    return value;
  }
  public totalUnit(): void {

  }

  // close(): void {
  //   this.dialogRef.close();
  // }

  public ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  resetAllCValues(): void {
    this.sizeRatio = "";
    this.comment = "";
    this.rating = "";
    this.dropdownRating = ""
  }

}
