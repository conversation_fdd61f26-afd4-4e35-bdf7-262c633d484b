import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserModule, Title } from '@angular/platform-browser';
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CdkTableModule } from '@angular/cdk/table';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
// import { AgmCoreModule } from '@agm/core';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';
import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { ToastrModule } from 'ngx-toastr';
import { NgProgressModule } from 'ngx-progressbar';
import { NgProgressHttpModule } from 'ngx-progressbar/http';
import { VirtualTourComponent } from '../app/virtual-tour/virtual-tour.component';

import 'hammerjs';
import 'mousetrap';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { MatChipsModule } from '@angular/material/chips';

import { AuthService } from './service/auth/auth.service';

import { ChankyaAppComponent } from './app.component';
import { RoutingModule } from "./app-routing.module";
import { MainComponent } from './main/main.component';
import { AuthComponent } from './auth/auth.component';
import { HorizontalLayoutComponent } from './horizontal-layout/horizontal-layout.component';
import { MenuToggleModule } from './core/menu/menu-toggle.module';
import { MenuItems } from './core/menu/menu-items/menu-items';
import { PageTitleService } from './core/page-title/page-title.service';
import { SessionDemoModule } from './session/session.module';
import { AppconfigComponent } from './appconfig/appconfig.component';
import { ModalModule, BsModalRef } from 'ngx-bootstrap/modal';
import { UserComponent } from './users/user.component';
import { VideoConfComponent } from './videoconf/videoconf.component';
import { VideoStreamComponent } from './videostream/videostream.component';
import { HomeComponent } from './home/<USER>';
import { PaymentComponent } from './payment/payment.component';

import { DirectivesModule } from './core/directive/directives.module';


import { ProductDetailsComponent } from './product-details/product-details.component';
import { CartComponent } from './cart/cart.component';
import { OrderGridComponent } from './order-grid/order-grid.component';

import { ContextMenuModule } from 'ngx-contextmenu';

import { FileSaverModule } from 'ngx-filesaver';
import { DatePipe } from '@angular/common';


import { SecuredImageComponent } from './util/SecuredImage.component';
import { BasicHttpInterceptor } from './util/BasicHttpInterceptor.component';

import { Globals, MessageService } from './appconfig/appconfig.component';
import { AppMaterialModule } from './app-material-module';
import { NgxFileDropModule } from 'ngx-file-drop';
import { NgxImageZoomModule } from 'ngx-image-zoom';
import { AngularFileUploaderModule } from "angular-file-uploader";

import { AuthGuard } from './core/guards/auth.guard';


import { NgSelectModule } from '@ng-select/ng-select';
import { NgxGalleryModule } from 'ngx-gallery-9';
import { DragScrollModule } from 'ngx-drag-scroll';
import { GalleryModule } from '@ks89/angular-modal-gallery';
import { NgxUsefulSwiperModule } from 'ngx-useful-swiper';
import { GalleryListManagementModule } from './gallery/gallery.module';
import { NgImageSliderModule } from 'ng-image-slider';
import { ImageZoomComponent } from './image-zoom/image-zoom.component';
import { SizeQuantitySharedComponent } from './size-quantity-shared/size-quantity-shared.component';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { WishlistComponent } from './wishlist/wishlist.component';

import { CartSearchbyStyleCodePipe } from './pipes/cart-searchby-style-code.pipe';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from './shared/shared.module';

import { BreadcrumbService } from './breadcrumb/breadcrumb.service';

import { HttpsStatusLoaderService, HTTPListener } from './service/http-status-loader.service';
import { OrderSummaryComponent } from './order-summary/order-summary.component';
const loader_HTTPInterceptor_Service = [HTTPListener, HttpsStatusLoaderService];



/********** Custom option for ngx-translate ******/
export function createTranslateLoader(http: HttpClient) {
	return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
	suppressScrollX: true
};

const perfectScrollbarConfig: PerfectScrollbarConfigInterface = {
	suppressScrollX: true
};

@NgModule({
	imports: [
		CommonModule,
		//MatAutocompleteModule,

		BrowserModule,
		BrowserAnimationsModule,
		FormsModule,
		NgSelectModule,
		ReactiveFormsModule,

		AppMaterialModule,
		CommonModule,
		CdkTableModule,
		RoutingModule,
		PerfectScrollbarModule,
		ContextMenuModule.forRoot({
			useBootstrap4: true,
		}),
		MenuToggleModule,
		HttpClientModule,
		TranslateModule.forRoot({
			loader: {
				provide: TranslateLoader,
				useFactory: createTranslateLoader,
				deps: [HttpClient]
			}
		}),
		ToastrModule.forRoot({
			timeOut: 2000,
			preventDuplicates: true
		}),
		NgxDatatableModule,
		ModalModule.forRoot(),
		FileSaverModule,
		NgxFileDropModule,
		NgxImageZoomModule,
		AngularFileUploaderModule,
		NgxGalleryModule,
		DragScrollModule,
		GalleryModule.forRoot(),
		NgxUsefulSwiperModule,
		GalleryListManagementModule,
		NgImageSliderModule,
		NgProgressModule,
		NgProgressHttpModule,
		DirectivesModule,
		MatChipsModule,
		NgbModule,
		SharedModule,
	],
	declarations: [
		ChankyaAppComponent,
		MainComponent,
		AuthComponent,
		HorizontalLayoutComponent,
		AppconfigComponent,
		SecuredImageComponent,
		VideoConfComponent,
		VideoStreamComponent,
		HomeComponent,
		PaymentComponent,
		ProductDetailsComponent,
		ImageZoomComponent,
		CartComponent,
		OrderGridComponent,
		SizeQuantitySharedComponent,
		BreadcrumbComponent,
		VirtualTourComponent,
		WishlistComponent,
		CartSearchbyStyleCodePipe,
		OrderSummaryComponent,

	],
	bootstrap: [ChankyaAppComponent],
	providers: [
		MenuItems,
		PageTitleService,
		DatePipe,
		AuthService,
		AuthGuard,
		{
			provide: PERFECT_SCROLLBAR_CONFIG,
			useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG
		},
		{
			provide: HTTP_INTERCEPTORS, useClass: BasicHttpInterceptor, multi: true
		},
		Globals,
		MessageService,
		BsModalRef,
		Title,
		BreadcrumbService,
		...loader_HTTPInterceptor_Service, {
			provide: HTTP_INTERCEPTORS,
			useClass: HTTPListener,
			multi: true
		}
	],
	exports: [
		ImageZoomComponent,
		SizeQuantitySharedComponent
	]
})
export class AppModule { }
