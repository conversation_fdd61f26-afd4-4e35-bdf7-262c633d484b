// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `angular-cli.json`.


export const environment = {
  production: false,
  // apiUrl: 'https://usiw.3frameslab.com',
  //apiUrl: 'http://************:8080',
  //apiUrl: 'http://***********:8080',
  // apiUrl: 'http://localhost:9191',
  //apiUrl: 'http://asusegr01.3frameslab.com:9292',
//  apiUrl: 'https://asapsgr01.3frameslab.com',
  // apiUrl: 'https://asusegr01.3frameslab.com', // Citrus, IDC, Stori, Twills
//  apiUrl: 'https://asusegr02.3frameslab.com', // usiw(<PERSON><PERSON>)
  // apiUrl: 'https://asapsgr01.3frameslab.com', // USPA, G&J, LifeStyle
  // apiUrl:'https://l0x3krvkrf.execute-api.ap-south-1.amazonaws.com',
  //apiUrl: 'http://aspada.3frameslab.com:9090', // Test
  // apiUrl: 'http://localhost:9191',
  apiUrl:'https://77dh1v6l1e.execute-api.ap-south-1.amazonaws.com',

 tenantUid: 'test',
  // tenantUid:'stori',
  // tenantUid: 'uspa',
  // tenantUid:'citrus',
//  tenantUid:'twillsroadshow',
//  tenantUid:'re',
  // tenantUid:'ul',
  // tenantUid: 'uspa',
  // tenantUid: 'turtle',
  // tenantUid:'turtlereadystock',
  //tenantUid: 'gajreadystock',
  // tenantUid: 'turtleroadshow',
  // tenantUid: 'arvind',
  // tenantUid: 'uspaaw21',
  // tenantUid:'usiw', //hanes,
  // tenantUid: 'indt',
  //tenantUid: 'raymondroadshow',
  // tenantUid: 'testparent',
  // tenantUid: 'usiw',
  readyStockTenantUid: 'gajreadystock',
  title: 'Roadshow SS21'
};
