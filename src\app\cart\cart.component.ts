import * as am4charts from '@amcharts/amcharts4/charts';
import * as am4core from '@amcharts/amcharts4/core';
import { Location } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from 'app/appconfig/appconfig.component';
import { Catalog } from 'app/model/catalog.model';
import { Order } from 'app/model/order.model';
import { OrderDetails } from 'app/model/OrderDetails.model';
import { OrderItem } from 'app/model/orderitem.model';
import { PaymentInfo } from 'app/model/PaymentInfo.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { User } from 'app/model/user.model';
import { CatalogService } from 'app/service/catalog.service';
import { OrderService } from 'app/service/order.service';
import { WindowRefService } from 'app/service/window-ref.service';
import { ToastrService } from 'ngx-toastr';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { SizeQuantitySharedComponent } from '../size-quantity-shared/size-quantity-shared.component';
import { ConfirmationDialogComponent } from '../shared/confirmation-dialog/confirmation-dialog.component';


@Component({
  selector: 'app-cart',
  templateUrl: './cart.component.html',
  styleUrls: ['./cart.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CartComponent implements OnInit, OnDestroy {

  productUid: string;
  product: Catalog;
  user: User
  isZoom: boolean = false;
  zoomImgSrc: string;
  isShowAmt: boolean = true;
  order: Order = new Order();

  imageUrl: string;
  imageUids: string[] = [];
  items: OrderItem[] = [];
  totalCount: number = 0;
  totalOptions: number = 0;
  totalValue: number = 0;
  isEdit: any = null;
  tenantConfig: TenantConfig;
  orderItems: OrderItem[] = []
  customers: User[] = []

  categoryRatingChart: am4charts.PieChart
  brandChart: am4charts.PieChart
  categoryChart: am4charts.XYChart3D

  chartMode: string = "OPT"
  selectedCategory: string = '';
  selectedBrand: string = '';

  searchStyleCodeText: any = "";

  // Debounce mechanism for cart updates
  private cartUpdateSubject = new Subject<any>();
  private cartUpdateSubscription: Subscription;

  // TODO:
  // 1. Add a summary section to collapse or expand
  // 2. display the charts on expansion of the section
  // 3. beautify the render the charts by by Qty or Options or by Amt.
  // 4. option to filter based selectedBrand and selectedCategory
  // 5. option to clear the filter.

  constructor(private router: Router, private toastrService: ToastrService, private winRef: WindowRefService,
    private route: ActivatedRoute, private catalogService: CatalogService, private orderService: OrderService,
    private _changeDetectorRef: ChangeDetectorRef,
    private location: Location, private globals: Globals, public dialog: MatDialog) {
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"))
    if (this.tenantConfig.disableAmt != null && this.tenantConfig.disableAmt != undefined && this.tenantConfig.disableAmt == 1) {
      this.isShowAmt = false
    }
    this.user = JSON.parse(localStorage.getItem("userProfile"));
    this.customers = JSON.parse(localStorage.getItem("customers"))
  }

  ngOnInit() {
    this.order = new Order();
    this.fetchItems();
    this.prepareBrandChart();
    this.prepareCategoryChart();
    this.prepareRatingChart();

    // Setup debounced cart update
    this.cartUpdateSubscription = this.cartUpdateSubject.pipe(
      debounceTime(300), // Wait for 300ms of inactivity
      distinctUntilChanged() // Only process if the data is different
    ).subscribe(items => {
      this.processCartUpdate(items);
    });
  }

  ngOnDestroy() {
    // Clean up subscription to prevent memory leaks
    if (this.cartUpdateSubscription) {
      this.cartUpdateSubscription.unsubscribe();
    }
  }

  fetchItems() {
    this.orderService.getCartSummary().subscribe(data => {
      this.items = data;
      this.calcTotal();
      this._changeDetectorRef.detectChanges();
    });
  }

  editItem(item: OrderItem) {

    this.catalogService.getByUid(item.catalogUid).subscribe(data => {
      this.product = data;
      let dialogRef = this.dialog.open(SizeQuantitySharedComponent, {
        width: '800px',
        data: {

        }
      });
      dialogRef.componentInstance.product = this.product
      dialogRef.componentInstance.isDialog = true
      dialogRef.componentInstance.sizeQuantityData.subscribe((data) => {
        // if (data.removeItemCatuid) {
        //   this.removeItem(data.removeItemCatuid);
        // } else {
        this.orderItems = data?.items;
        if (this.orderItems?.length > 0)
          this.updateCart(data?.items);
        //}
      });
    });

  }

  sendReport() {
    this.orderService.sendReport().subscribe(data => {
      this.toastrService.success('', 'Order report sent successfully!');
    });
  }

  preSubmit() {
    // Open confirmation dialog before proceeding with Ship To dropdown, expiry date, and remarks
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '600px',
      data: {
        title: 'Order Submission',
        // message: 'Are you sure you want to submit this order?',
        confirmText: 'Submit',
        cancelText: 'Cancel',
        showShipToField: true,
        customerUid: this.user?.customerUid || '8002', // Pass customerUid for API integration, fallback to 8002
        shipToOptions: this.customers, // Fallback options if API fails
        onSaveNewShipTo: (newShipTo) => this.saveNewShipToAddress(newShipTo)
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.confirmed) {
        // User confirmed, proceed with order submission
        // We can use result.shipTo for the selected shipping address
        // console.log('Selected shipping address:', result.shipTo);
        // console.log('Ship To ID:', result.shipToId);
        // console.log('Expiry date:', result.expiryDate);
        // console.log('Remarks:', result.remarks);

        // Store the expiry date and remarks in the order object
        if (this.order) {
          this.order.expiryDate = result.expiryDate;
          this.order.remarks = result.remarks;
          // We could also store the ship to ID in the order object if needed
        }

        let isPaymentEnabled = false
        for (var i = 0; i < this.customers.length; i++) {
          if (this.customers[i].enablePayment != null && this.customers[i].enablePayment == "1") {
            isPaymentEnabled = true
          }
        }
        if (!isPaymentEnabled && this.tenantConfig.enablePayment == '1') {
          isPaymentEnabled = true
        }

        if (isPaymentEnabled) {
          this.createRzpayOrder(result.shipTo, result.expiryDate, result.remarks, result.shipToId, result.shipToName);
        } else {
          // Pass the selected shipping address, ship to ID, ship name, expiry date, and remarks to the submit method
          this.submit(result.shipTo, result.expiryDate, result.remarks, result.shipToId, result.shipToName);
        }
      }
      // If result is false or undefined, do nothing (user cancelled)
    });
  }

  submit(shipTo?: any, expiryDate?: string, remarks?: string, shipToId?: string, shipToName?: string) {
    for (var i = 0; i < this.items.length; ++i) {
      if (this.items[i].qty == undefined || this.items[i].qty == 0) {
        this.toastrService.warning('', 'Order Qty is empty');
        //return
      }
    }

    // If a shipping address was selected, we can use it here
    if (shipTo) {
      // console.log('Using shipping address for order:', shipTo);
      // You can modify the order with the shipping address if needed
      // For example, you might want to set the shipping address on the order
      // this.order.shippingAddress = shipTo.address1;
      // this.order.shippingCity = shipTo.cityName;
      // etc.
    }

    // If a ship to ID was provided, log it
    // if (shipToId) {
    //   console.log('Using ship to ID for order:', shipToId);
    //   // You can store the ship to ID in the order object if needed
    // }

    // Set expiry date and remarks if provided
    if (expiryDate) {
      // console.log('Using expiry date for order:', expiryDate);
      if (this.order) {
        this.order.expiryDate = expiryDate;
      }
    }

    if (remarks) {
      // console.log('Using remarks for order:', remarks);
      if (this.order) {
        this.order.remarks = remarks;
      }
    }

    // Create a payload object that includes all the necessary data
    const payload: any = {};

    if (expiryDate) {
      payload.expiryDate = expiryDate;
    }

    if (remarks) {
      payload.remarks = remarks;
    }

    if (shipToId) {
      payload.shipToId = shipToId;
    }

    //this.orderService.updateOrder(updatedOrder).subscribe(data => { });
    this.orderService.submitPendingOrder(expiryDate, remarks, shipToId, shipToName).subscribe(data => {
      this.router.navigate(['/h/orderGrid']);
      this.toastrService.success('', 'Order created successfully!');
    });
  }

  cancel() {
    //this.location.back();
    this.router.navigate(['/h/collList/*']);
  }

  calcTotal() {
    this.totalCount = 0;
    this.totalOptions = 0;
    this.totalValue = 0;
    for (let i of this.items) {
      this.totalOptions = parseInt(this.totalOptions + '') + parseInt(i.options + '');
      this.totalCount = parseInt(this.totalCount + '') + parseInt(i.qty + '');
      this.totalValue = parseFloat(this.totalValue + '') + (i.price ? parseFloat(i.price + '') : 0);
    }
    this.reloadChartData()
  }
  private prepareTotalCount(item) {
    if (item.multiplier) {
      return parseInt((item.qty * item.multiplier) + '');
    } else {
      return parseInt(item.qty + '');
    }
  }
  private prepareTotalValue(item) {
    if (item.multiplier) {
      return parseInt(item.price * (item.qty * item.multiplier) + '');
    } else {
      return parseInt(item.price * item.qty + '');
    }
  }

  prepareQuantity(item) {
    if (item.multiplier) {
      return item.qty * item.multiplier;
    } else {
      return item.qty;
    }
  }

  preparePrice(item) {
    if (item.multiplier) {
      return item.price * (item.qty * item.multiplier);
    } else {
      return (item.qty * item.price);
    }
  }


  viewDet(item: OrderItem) {
    this.router.navigate(['/h/productDetails/' + item.catalogUid]);
  }

  getPrice(item: Catalog) {
    if (item.fromPrice != undefined && item.toPrice != undefined && item.fromPrice != item.toPrice) {
      return "&#x20b9;" + item.fromPrice + " to  &#x20b9;" + item.toPrice;
    } else {
      if (item.fromPrice != undefined && item.toPrice == undefined) {
        return "&#x20b9;" + item.fromPrice;
      }
      return "&#x20b9;" + item.toPrice;
    }
  }

  deleteItem(item: OrderItem) {
    var items: OrderItem[] = [];
    items.push(item);
    this.orderService.deleteOrderItem(items).subscribe(data => {
      this.toastrService.success('', 'Deleted order item successfully!');
      // this.catalogService.getPendingOrder().subscribe(data => {
      //   this.order = data;
      //   this.data.addCart("0");
      // });
    });
  }

  public filterByBrand(brand): void {

  }

  selectMode(evt) {
    this.chartMode = evt
    this.categoryRatingChart.data = []
    this.categoryChart.data = []
    this.selectedCategory = ''
    this.selectedBrand = ''
  }

  canDisplay(item) {
    if (this.selectedCategory == '' && this.selectedBrand == '') {
      return true
    }
    if (this.selectedCategory != '' && item.subCatUid == this.selectedCategory) {
      return true
    }
    if (this.selectedCategory == '' && this.selectedBrand != '' && item.catUid == this.selectedBrand) {
      return true
    }


    return false

  }

  removeCategory() {
    this.selectedCategory = ''
    this.categoryChart.data = []
    this.categoryRatingChart.data = []
  }

  removeBrand() {
    this.selectedBrand = ''
    this.selectedCategory = ''
  }

  prepareBrandChart() {

    this.brandChart = am4core.create("chartdiv", am4charts.PieChart);
    this.brandChart.data = []
    let title: any = this.brandChart.titles.create();
    title.text = "Sub-brand wise summary";
    title.fontSize = 16;
    title.fontWeight = "600";
    title.marginBottom = 20;
    let pieSeries = this.brandChart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "qty";
    pieSeries.dataFields.category = "name";
    let obj = this
    pieSeries.slices.template.events.on("hit", function (ev) {
      if (ev.target.dataItem) {
        let i = ev.target.dataItem;
        let j = i as am4charts.PieSeriesDataItem
        obj.reloadCategoryChartData(j.category)
        obj.reloadCategoryRatingChartData(j.category)
        obj.selectedBrand = j.category;
      }
    });
  }

  prepareRatingChart() {
    this.categoryRatingChart = am4core.create("categoryRatingChartDiv", am4charts.PieChart);
    this.categoryRatingChart.data = []
    let title: any = this.categoryRatingChart.titles.create();
    title.text = "Rating wise summary";
    title.fontSize = 16;
    title.fontWeight = "600";
    title.marginBottom = 20;
    let pieSeries = this.categoryRatingChart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "qty";
    pieSeries.dataFields.category = "name";
    let obj = this
    pieSeries.slices.template.events.on("hit", function (ev) {
      if (ev.target.dataItem) {
        let i = ev.target.dataItem
        let j = i as am4charts.PieSeriesDataItem
        // obj.reloadCategoryChartData(j.category)
        // obj.reloadCategoryRatingChartData(j.category)
      }
    });
  }


  prepareCategoryChart() {
    let chart = am4core.create("categoryChartDiv", am4charts.XYChart3D);
    this.categoryChart = chart
    this.categoryChart.data = []
    let title: any = this.categoryChart.titles.create();
    title.text = "Category wise summary";
    title.fontSize = 16;
    title.fontWeight = "600"
    title.marginBottom = 20;
    // Create axes
    let categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "name";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.renderer.labels.template.hideOversized = false;
    categoryAxis.renderer.minGridDistance = 20;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.tooltip.label.rotation = 270;
    categoryAxis.tooltip.label.horizontalCenter = "right";
    categoryAxis.tooltip.label.verticalCenter = "middle";

    let name = "Qty"
    if (this.chartMode == "OPT") {
      name = "Options"
    } else if (this.chartMode == "AMT") {
      name = "Amount"
    }

    let valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = name;
    valueAxis.title.fontWeight = "bold";

    // Create series
    let series = chart.series.push(new am4charts.ColumnSeries3D());
    series.dataFields.valueY = "qty";
    series.dataFields.categoryX = "name";
    series.name = name;
    series.tooltipText = "{categoryX}: [bold]{valueY}[/]";
    series.columns.template.fillOpacity = .8;
    series.columns.template.width = am4core.percent(30);

    series.columns.template.events.on("hit", function (ev) {
      let i = ev.target.dataItem
      let j = i as am4charts.ColumnSeriesDataItem
      this.selectedCategory = j.categoryX
    }, this);

    let columnTemplate = series.columns.template;
    columnTemplate.strokeWidth = 2;
    columnTemplate.strokeOpacity = 1;
    columnTemplate.stroke = am4core.color("#FFFFFF");

    columnTemplate.adapter.add("fill", function (fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    })

    columnTemplate.adapter.add("stroke", function (stroke, target) {
      return chart.colors.getIndex(target.dataItem.index);
    })

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.strokeOpacity = 0;
    chart.cursor.lineY.strokeOpacity = 0;

  }

  reloadChartData() {
    let map = new Map()

    for (let item of this.items) {
      let val = 0
      if (this.chartMode == "QTY") {
        val = item.qty
      } else if (this.chartMode == "OPT") {
        val = item.qty
      } if (this.chartMode == "AMT") {
        val = item.qty * item.price
      }
      if (map.has(item.catUid)) {
        map.set(item.catUid, map.get(item.catUid) + val)
      } else {
        map.set(item.catUid, val)
      }
    }

    let data = []
    map.forEach((value: number, key: string) => {
      data.push({
        "name": key,
        "qty": value
      })
    });

    this.brandChart.data = data
  }

  reloadCategoryChartData(brand) {

    let name = "Qty"
    if (this.chartMode == "OPT") {
      name = "Options"
    } else if (this.chartMode == "AMT") {
      name = "Amount"
    }

    let valueAxis = this.categoryChart.yAxes.values[0];
    valueAxis.title.text = name;

    let series = this.categoryChart.series.values[0];
    series.name = name;

    let map = new Map()
    for (let item of this.items) {
      let val = 0
      if (item.catUid == brand) {
        if (this.chartMode == "QTY") {
          val = item.qty
        } else if (this.chartMode == "OPT") {
          val = item.qty
        } if (this.chartMode == "AMT") {
          val = item.qty * item.price
        }
        if (map.has(item.subCatUid)) {
          map.set(item.subCatUid, map.get(item.subCatUid) + val)
        } else {
          map.set(item.subCatUid, val)
        }
      }
    }

    let data = []
    map.forEach((value: number, key: string) => {
      data.push({
        "name": key,
        "qty": value
      })
    });
    this.categoryChart.data = data
  }

  reloadCategoryRatingChartData(brand) {

    let map = new Map()
    for (let item of this.items) {
      if (item.catUid == brand) {
        let rating = "NA"
        if (item.rating != undefined && item.rating != null && item.rating != '') {
          rating = item.rating
        }
        if (map.has(rating)) {
          map.set(rating, map.get(rating) + 1)
        } else {
          map.set(rating, 1)
        }
      }
    }

    let data = []
    map.forEach((value: number, key: string) => {
      data.push({
        "name": key,
        "qty": value
      })
    });
    this.categoryRatingChart.data = data
  }

  createRzpayOrder(shipTo?: any, expiryDate?: string, remarks?: string, shipToId?: string, shipToName?: string) {
    // If a shipping address was selected, we can use it here
    if (shipTo) {
      console.log('Using shipping address for payment:', shipTo);
      // You can modify the payment info with the shipping address if needed
    }

    // If a ship to ID was provided, log it
    if (shipToId) {
      console.log('Using ship to ID for payment:', shipToId);
    }

    // Set expiry date and remarks if provided
    if (expiryDate) {
      console.log('Using expiry date for payment:', expiryDate);
      if (this.order) {
        this.order.expiryDate = expiryDate;
      }
    }

    if (remarks) {
      console.log('Using remarks for payment:', remarks);
      if (this.order) {
        this.order.remarks = remarks;
      }
    }

    this.orderService.initPayment().subscribe(paymentInfo => {
      this.payWithRazor(paymentInfo, expiryDate, remarks, shipToId, shipToName);
    });
  }

  payWithRazor(paymentInfo: any, expiryDate?: string, remarks?: string, shipToId?: string, shipToName?: string) {
    const options: any = {
      key: paymentInfo.apiKey,
      amount: paymentInfo.amount,
      currency: paymentInfo.currency,
      name: paymentInfo.customerName,
      description: paymentInfo.description,
      image: './assets/logo.png',
      order_id: paymentInfo.paymentOrderUid,
      modal: {
        escape: false,
      },
      notes: {
      },
      theme: {
        color: '#0c238a'
      }
    };
    options.handler = ((response: any) => {
      options.response = response;
      var payment = new PaymentInfo();
      payment.paymentId = response.razorpay_payment_id;
      payment.paymentOrderUid = response.razorpay_order_id;
      payment.signature = response.razorpay_signature;
      this.orderService.verifyPayment(payment).subscribe(() => {
        // After payment verification, submit the order with expiry date, remarks, ship to ID, and ship name
        this.orderService.submitPendingOrder(expiryDate, remarks, shipToId, shipToName).subscribe(() => {
          this.router.navigate(['/h/orderGrid']);
          this.toastrService.success('', 'Order created successfully!');
        });
      });
    });
    options.modal.ondismiss = (() => {
      // handle the case when user closes the form while transaction is in progress
    });
    const rzp = new this.winRef.nativeWindow.Razorpay(options);
    rzp.open();
  }

  public getSizeQuantity(data): void {

  }

  getImg(item): string {
    if (item.imageUid == null || item.imageUid == undefined) {
      return this.tenantConfig.imageBaseUrl + item.catalogUid + ".jpg"
    }
    var idx = (item.imageUid + ",").indexOf(",")
    var img = item.imageUid.substring(0, idx)
    return this.tenantConfig.imageBaseUrl + img
  }

  updateCart(oi) {
    // Instead of making the API call directly, pass the items to the subject
    this.cartUpdateSubject.next(oi);
  }

  // This method will be called after debouncing
  private processCartUpdate(oi) {
    var det = new OrderDetails();
    var ord = new Order();
    ord.customerUid = this.user.customerUid;
    det.order = ord;
    det.items = oi;
    this.orderService.saveOrUpdateOrder(det).subscribe(data => {
      this.toastrService.success('', 'Updated cart successfully');
      setTimeout(() => {
        this.fetchItems();
      }, 400);
    });
  }
  removeItem(item) {
    this.orderService.deleteOrderItemByCatalogUid(item.catalogUid).subscribe(data => {
      this.toastrService.success('', 'Deleted item successfully');
      this.fetchItems()
    });
  }

  public viewOrderDetailsFromCart(item): void {
    this.router.navigate(['/h/prodDet/' + item.catalogUid]);
  }

  /**
   * Save a new shipping address
   * This method is called when a user creates a new shipping address from the order confirmation dialog
   */
  saveNewShipToAddress(newShipTo: any): void {
    console.log('Saving new shipping address:', newShipTo);

    // Add the customer UID to the new shipping address
    if (this.user && this.user.customerUid) {
      newShipTo.customerUid = this.user.customerUid;
    }

    // Set default values for any required fields in the backend
    // This ensures compatibility with the existing system
    if (!newShipTo.cityName) newShipTo.cityName = '';
    if (!newShipTo.state) newShipTo.state = '';
    if (!newShipTo.zipCode) newShipTo.zipCode = '';

    // Here you would typically call a service to save the address to the backend
    // For example:
    // this.customerService.saveShippingAddress(newShipTo).subscribe(
    //   (savedAddress) => {
    //     this.toastrService.success('', 'Shipping address saved successfully');
    //     // The address is already added to the dropdown in the dialog
    //   },
    //   (error) => {
    //     this.toastrService.error('', 'Failed to save shipping address');
    //     console.error('Error saving shipping address:', error);
    //   }
    // );

    // For now, we'll just add it to the customers array in memory
    if (!this.customers) {
      this.customers = [];
    }

    // Add the new address to the customers array
    this.customers.push(newShipTo);

    this.toastrService.success('', 'New shipping address added successfully');
  }



}




// @Component({
//   selector: 'dialog-content-example-dialog',
//   templateUrl: 'cart-order-item.dialog.html',
// //      changeDetection: ChangeDetectionStrategy.OnPush,
// })
// export class CartOrderItemDialog {

//   @Input() product:Catalog = new Catalog();

//   constructor(private catalogService: CatalogService, private orderService: OrderService, private cdr: ChangeDetectorRef,
//     private location: Location, private globals:Globals,public dialog: MatDialog) {
//     //this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"))
//   }

//   ngOnInit(): void {

//         this.ngOnChanges();
//         this.cdr.detectChanges()
//   }

//   public getSizeQuantity(data):void {
//    console.log(data);
//   }

//   ngOnChanges() {
//     console.log(this.product)
//   }

// }
