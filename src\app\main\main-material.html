<!-- Main--> 

<div class="app {{headerSkinColor}} icon-sidebar-wrap" [dir]="layout" [ngClass]="themeSkinColor" [class.app-dark]="dark" [class.boxed]="boxed"
   [class.collapsed-sidebar]="collapseSidebar" [class.compact-sidebar]="compactSidebar" [class.customizer-in]="customizerIn"
   [class.chat-window--open]="chatWindowOpen" [class.sidebar-closed]="sidebarClosed" [class.sidebar-opened]="!sidebarClosed" [class.chat-sidebar]="chatSidebar">
   <!-- Sidebar -->
   <div class="chankya-customizer">
      <a class="customizer-toggle bg-primary faa-parent animated-hover" (click)="customizerFunction()">
         <i class="fa fa-wrench faa-slow faa-wrench" aria-hidden="true"></i>
      </a>
      <!-- Nav tabs -->
      <perfect-scrollbar class="settings-panel">
         <div class="theme-options">
            <div class="theme-head">
               <h4>{{'Theme Settings'|translate}}</h4>
            </div>
            <div class="form-group lang-change">
               <label for="exampleSelect1">{{'Select Language'|translate}}</label>
               <select class="layout-item custom-select mt-2 form-control mt-1 rounded-0" placeholder="Language" [(ngModel)]="currentLang" #langSelect="ngModel" (ngModelChange)="translate.use(currentLang)">
                  <option *ngFor="let lang of translate.getLangs()" [value]="lang">{{ lang }}</option>
               </select>
            </div>
            <div class="layout-item mb-4 lang-change">
               <div class="more-theme-style mb-2">
                  <label class="mb-2">{{'Choose Sidebar Color'|translate}}</label>
                  <div class="d-flex align-items-center">
                     <a href="javascript:void(0)" class="theme default d-block mb-2 mr-1" (click)="changeThemeColor('default')"><i class="fa fa-circle fa-lg text-light" aria-hidden="true"></i> </a>
                     <a href="javascript:void(0)" class="theme primary d-block mb-2 mr-1" (click)="changeThemeColor('primary')"><i class="fa fa-circle fa-lg text-primary" aria-hidden="true"></i></a>
                     <a href="javascript:void(0)" class="theme success d-block mb-2 mr-1" (click)="changeThemeColor('success')"><i class="fa fa-circle fa-lg text-success" aria-hidden="true"></i></a>
                     <a href="javascript:void(0)" class="theme warning d-block mb-2 mr-1" (click)="changeThemeColor('warning')"><i class="fa fa-circle fa-lg text-warning" aria-hidden="true"></i></a>
                  </div>
               </div>
               <div class="more-theme-style mb-2">
                  <label class="mb-2">{{'Choose Header Color'|translate}}</label>
                  <div class="d-flex align-items-center">
                     <a href="javascript:void(0)" class="theme default d-block mb-2 mr-1" (click)="changeHeaderColor('default')"><i class="fa fa-circle fa-lg text-light" aria-hidden="true"></i></a>
                     <a href="javascript:void(0)" class="theme header-primary d-block mb-2 mr-1" (click)="changeHeaderColor('header-primary')"><i class="fa fa-circle fa-lg text-primary" aria-hidden="true"></i></a>
                     <a href="javascript:void(0)" class="theme header-success d-block mb-2 mr-1" (click)="changeHeaderColor('header-success')"><i class="fa fa-circle fa-lg text-success" aria-hidden="true"></i></a>
                     <a href="javascript:void(0)" class="theme header-warning d-block mb-2 mr-1" (click)="changeHeaderColor('header-warning')"><i class="fa fa-circle fa-lg text-warning" aria-hidden="true"></i></a>
                  </div>
               </div>
            </div>
            <div class="layout-item form-check mb-3" id="icon-sidebar">
               <label class="form-check-label">
                  <input (change)="toggleSidebar($event.target.checked)" class="form-check-input" type="checkbox" value="">
                  {{'Toggle Sidebar'|translate}}
               </label>
            </div>
            <div class="layout-item form-check mb-3 collapse-icon-disabled">
               <label class="form-check-label">
                  <input [(ngModel)]="collapseSidebar" (change)="compactSidebar = false" class="form-check-input" type="checkbox" value="">
                  {{'Collapsed Sidebar'|translate}}
               </label>
            </div>
            <div class="layout-item boxed-layout-md form-check mb-3 disabled-checkbox" id="boxed-layout">
               <label class="form-check-label">
                  <input (change)="boxedLayout($event.target.checked)" class="form-check-input" type="checkbox" value=""> {{'Boxed Layout'|translate}}
               </label>
            </div>
            <div class="layout-item form-check mb-3">
               <label class="form-check-label">
                  <input [(ngModel)]="dark" class="form-check-input" type="checkbox" value=""> {{'Dark Mode'|translate}}
               </label>
            </div>
            <div class="layout-item form-check mb-3">
               <label class="form-check-label">
                  <input (change)="changeRTL($event.target.checked)" class="form-check-input" type="checkbox" value="">
                  {{'RTL'|translate}}
               </label>
            </div>
         </div>
         <!-- theme option closed -->
      </perfect-scrollbar>
      <!-- setting panel Closed -->
   </div>
   <!-- Chankya customize -->
   <div class="chat-sidebar-pan">
      <div class="settings-panel">
         <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
               <a class="nav-link active" data-toggle="tab" href="#chat" role="tab">{{'Chat'|translate}}</a>
            </li>
            <li class="nav-item">
               <a class="nav-link" data-toggle="tab" href="#setting" role="tab">{{'Settings'|translate}}</a>
            </li>
         </ul>
         <div class="tab-content">
            <div class="tab-pane active" id="chat" role="tabpanel">
               <div class="chat-tabs">
                  <a href="javascript:void(0)" (click)="chatWindowFunction()">
                     <div class="d-flex justify-content-start">
                        <div class=""> <img class="img-responsive rounded-circle" alt="Chat session" src="assets/img/user-1.jpg"
                              width="50" height="50"> </div>
                        <div class="px-2">
                           <span class="d-block">Connor Soto</span> <small class="d-block">See you later</small>
                        </div>
                     </div>
                  </a>
                  <a href="javascript:void(0)" (click)="chatWindowFunction()">
                     <div class="d-flex justify-content-start">
                        <div class=""> <img class="rounded-circle" alt="Chat session" src="assets/img/user-1.jpg"
                              width="50" height="50"> </div>
                        <div class="px-2">
                           <span class="d-block">Miguel Romero</span> <small class="d-block">Hope for best!</small>
                        </div>
                     </div>
                  </a>
                  <a href="javascript:void(0)" (click)="chatWindowFunction()">
                     <div class="d-flex justify-content-start">
                        <div class=""> <img class="rounded-circle" alt="Chat session" src="assets/img/user-2.jpg"
                              width="50" height="50"> </div>
                        <div class="px-2">
                           <span class="d-block">Marguerite Riley</span> <small class="d-block">Good bye and TC!</small>
                        </div>
                     </div>
                  </a>
                  <a href="javascript:void(0)" (click)="chatWindowFunction()">
                     <div class="d-flex justify-content-start">
                        <div class=""><img class="rounded-circle" alt="Chat session" src="assets/img/user-3.jpg"
                              width="50" height="50"> </div>
                        <div class="px-2">
                           <span class="d-block">Marian Burke</span> <small class="d-block">Talk u later</small>
                        </div>
                     </div>
                  </a>
                  <a href="javascript:void(0)" (click)="chatWindowFunction()">
                     <div class="d-flex justify-content-start">
                        <div class=""><img class="rounded-circle" alt="Chat session" src="assets/img/user-4.jpg"
                              width="50" height="50"> </div>
                        <div class="px-2">
                           <span class="d-block">Wayne Hoffman</span> <small class="d-block">Ready for sunday</small>
                        </div>
                     </div>
                  </a>
                  <a href="javascript:void(0)" (click)="chatWindowFunction()">
                     <div class="d-flex justify-content-start">
                        <div class=""> <img class="rounded-circle" alt="Chat session" src="assets/img/user-5.jpg"
                              width="50" height="50"> </div>
                        <div class="px-2 ">
                           <span class="d-block">Chris Evans</span> <small class="d-block">am fine ,thanks</small>
                        </div>
                     </div>
                  </a>
               </div>
            </div>
            <div class="tab-pane" id="setting" role="tabpanel">
               <div class="system-setting">
                  <div class="setting-header text-muted"><i class="fa fa-cog"></i> System</div>
                  <ul class="list-style-none pl-0">
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Nofications</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-nofications"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-nofications">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Quick Results</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-results"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-results">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Auto Updates</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-updates"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-updates">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                  </ul>
                  <div class="setting-header mt-3 text-muted"> <i class="fa fa-user"></i> Account</div>
                  <ul class="list-style-none pl-0">
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Offline Mode</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-offline"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-offline">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Location Share</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-location"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-location">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Show Offline User</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-offline-user"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-offline-user">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                     <li class="d-flex justify-content-between">
                        <div class="align-self-center">Save History</div>
                        <div class="onoffswitch align-self-center">
                           <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="switch-history"
                              checked>
                           <label class="onoffswitch-label mb-0" for="switch-history">
                              <span class="onoffswitch-inner"></span>
                              <span class="onoffswitch-switch"></span>
                           </label>
                        </div>
                     </li>
                  </ul>
               </div>
            </div>
         </div>
      </div>
      <!--setting panel closed -->
   </div>
   <!-- Chat Sidebar -->
   <div class="modal fade search-modal" id="search-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
      <div class="modal-dialog" role="document">
         <div class="container">
            <div class="mb-5">
               <input class="overlay-search" placeholder="Search...">
            </div>
            <div class="search-suggestion">
               <span class="d-block mb-3"><strong>Sugguestions</strong></span>
               <ul class="row search-list">
                  <li class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4">
                     <a href="javascript:void(0)"><span class="d-inline-block square-50 circle bg-success">A</span><span
                           class="search-filter mx-1">admin themes</span> in angular4</a>
                     <span class="d-block">@themeforest</span>
                  </li>
                  <li class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4">
                     <a href="javascript:void(0)"><span class="d-inline-block square-50 circle bg-success">H</span><span
                           class="search-filter mx-1">5 Star Hotels</span> in hotal</a>
                     <span class="d-block">@abcdehotels</span>
                  </li>
                  <li class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4">
                     <a href="javascript:void(0)"><span class="d-inline-block square-50 circle bg-success">F</span><span
                           class="search-filter mx-1">air ticket</span> in flights</a>
                     <span class="d-block">@airindia</span>
                  </li>
                  <li class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4">
                     <a href="javascript:void(0)"><span class="d-inline-block square-50 circle bg-success">D</span><span
                           class="search-filter mx-1">css design</span> in designs</a>
                     <span class="d-block">@csstricks</span>
                  </li>
                  <li class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4">
                     <a href="javascript:void(0)"><span class="d-inline-block square-50 circle bg-success">C</span><span
                           class="search-filter mx-1">programming</span> in code</a>
                     <span class="d-block">@abcdproject</span>
                  </li>
                  <li class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4">
                     <a href="javascript:void(0)"><span class="d-inline-block square-50 circle bg-success">M</span><span
                           class="search-filter mx-1">dunkirk</span> in movies</a>
                     <span class="d-block">@imdb</span>
                  </li>
               </ul>
            </div>
         </div>
      </div>
   </div>
   <!-- Modal -->
   <div class="chat-window">
      <div class="chat-window-i">
         <div class="user-name-chat bg-grey d-flex justify-content-between">
            <span class="align-self-center">Dekota James</span> <a href="javascript:void(0)" (click)="chatWindowFunction()"><i
                  class="material-icons align-self-center">close</i></a>
         </div>
         <div class="chat-inner">
            <ul>
               <li class="user-msg mb-3">
                  <div class="d-flex">
                     <img src="assets/img/user-4.jpg" class="circle chat-user-img" width="55" height="55" alt="user-image">
                     <div class="msg-detail px-2">
                        <span class="text-primary d-block mb-1">Dekota</span>
                        <div class="box-shadow p-2 msg-box mb-1">
                           <p>
                              Lorem ipsum dolor sit amet, consectetur adipisicing elit. Placeat, magni?
                           </p>
                        </div>
                        <div class="time text-muted"><small>3 min ago</small></div>
                     </div>
                  </div>
               </li>
               <li class="my-msg mb-3">
                  <div class="d-flex">
                     <img src="assets/img/user-2.jpg" class="circle chat-user-img" width="55" height="55" alt="user-image">
                     <div class="msg-detail px-2">
                        <span class="text-primary d-block mb-1">Admin</span>
                        <div class="box-shadow p-2 msg-box mb-1">
                           <p>
                              Lorem ipsum dolor sit amet, consectetur adipisicing.
                           </p>
                        </div>
                        <div class="time text-muted"><small>3 min ago</small></div>
                     </div>
                  </div>
               </li>
            </ul>
         </div>
         <div class="send-msg-chat p-3">
            <input class="form-control form-control-lg mb-3" placeholder="Type message" type="text">
            <button class="btn btn-primary" type="submit">Send</button>
         </div>
      </div>
   </div>
   <!-- Chat Window -->
</div>
<!-- app closed -->