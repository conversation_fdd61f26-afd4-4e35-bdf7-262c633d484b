.ngx-datatable.bootstrap {
    box-shadow: none;
    font-size: 13px;
    .datatable-header {
      height: unset !important;
      .datatable-header-cell {
        vertical-align: bottom;
        padding: 0.75rem;
        border-bottom: 1px solid #d1d4d7;
        .datatable-header-cell-label {
          line-height: 24px;
        }
      }
    }
    .datatable-body {
      .datatable-body-row {
        padding: 0.75rem;
        vertical-align: top;
        border-top: 1px solid #d1d4d7;
        &.datatable-row-even {
          background-color: rgba(0, 0, 0, 0.05);
        }
        &.active {
          background-color: #1862C6;
          color: #FFF;
        }
        .datatable-body-cell {
          text-align: left;
          vertical-align: top;
        }
      }
    }
    .datatable-footer {
      background: #424242;
      color: #ededed;
      margin-top: -1px;
      .page-count {
        line-height: 50px;
        height: 50px;
        padding: 0 1.2rem;
      }
      .datatable-pager {
        margin: 0 10px;
        vertical-align: top;
        ul {
          li {
            margin: 10px 0px;
            &:not(.disabled) {
              &.active,
              &:hover {
                a {
                  background-color: #545454;
                  font-weight: bold;
                }
              }
            }
          }
        }
        a {
          height: 22px;
          min-width: 24px;
          line-height: 22px;
          padding: 0;
          border-radius: 3px;
          margin: 0 3px;
          text-align: center;
          vertical-align: top;
          text-decoration: none;
          vertical-align: bottom;
          color: #ededed;
        }
        .datatable-icon-left {
          color: #FFF;
        }
        .datatable-icon-skip,
        .datatable-icon-right,
        .datatable-icon-prev {
          font-size: 18px;
          line-height: 27px;
          padding: 0 3px;
        }
      }
    }
  } 
  
  .form-row{
    margin-bottom: 0px;
  }
  
  .ngx-datatable .datatable-header {
    background: #1862c6;
    color: #fff;
    border: #1862c6;
  }
  
  .p-2 .mb-2 {
    padding: 0 0.5rem 0.5rem 0.5rem;
  }
  
  .ngx-datatable .datatable-footer .datatable-pager {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 80%;
    flex: 1 1 80%;
    text-align: center;
    padding: 0 355px 0 0px;
  }
  
  .mat-field-col {
    display: flex;
    flex-direction: column;
  }
  
  .mat-field-full-width {
    width: 100%;
  }
  
  .mat-input-element{
  width: 100% !important;
  }