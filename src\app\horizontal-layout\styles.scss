.link-item-selected {
  // color: #0e0e0e !important;
  border-radius: 10px 10px 0 0;
}

.horizontal-wrapper .chankya-base-container {
  padding-top: 0;
  overflow: auto;
  background-color: #e6e6e6;
  width: 100%;
}
.chankya-base-container {
  padding-top: 0;
  overflow: auto;
  background-color: #fff;
  width: 100%;
}

.ngx-datatable.bootstrap .datatable-header .datatable-header-cell {
  vertical-align: bottom;
  padding: 0.75rem;
  border-bottom: 1px solid #1862c6;
}

.inner-container {
  // background-color: #e6e6e6;
  // width: 100%;
}
.width100 {
  width: 100%;
}

.active-link {
  /*VF:background: #1862c6;*/
  border-bottom-color: #f26a10;
  border-bottom-style: solid;
  border-bottom-width: 4px;
}
a {
  text-decoration: none;
}
.horizontal-nav .horizontal-menu > li {
  display: inline-block;
  position: relative;
  list-style: none;
  margin: 0px 12px;
  //height: 31px;
  //padding: 10px !important;
  border-radius: 5px;
}
.horizontal-nav .horizontal-menu > li > a {
  // padding: 10px !important;
  border-right: 0px solid #dee4e8;
  line-height: 10px;
  font-size: 13px;
  cursor: pointer;
  font-weight: 600;
}
.verticalNavigationClass {
  list-style-type: none;
}
.verticalNavigationClass li {
  padding: 10px !important;
  margin: 5px 0px;
}

// .child-menu li:hover{
//   background:#a41e34;
//   color: #fff;
//   transition: 0.4ms ease-in;
//   border-radius: 10px 10px 10px 10px;
//   cursor: pointer;
// }

.vertical-menu-list-item:hover {
  background: #a41e34;
  color: #fff;
  transition: 0.4ms ease-in;
  border-radius: 10px 10px 0 0;
  cursor: pointer;
}

@media only screen and (max-width: 600px) {
  .vertical-navigation-menu {
    display: block;
  }
  .horizontal-navigation-menu {
    display: none;
  }
}
@media only screen and (min-width: 600px) {
  .vertical-navigation-menu {
    display: none;
  }
  .horizontal-navigation-menu {
    display: block;
  }
}
.brand-class {
  // background-image: -webkit-gradient( linear, left top, right top, color-stop(0, #f22), color-stop(0.15, #f2f), color-stop(0.3, #22f), color-stop(0.45, #2ff), color-stop(0.6, #2f2),color-stop(0.75, #2f2), color-stop(0.9, #ff2), color-stop(1, #f22) ) !important;
  // background-image: gradient( linear, left top, right top, color-stop(0, #f22), color-stop(0.15, #f2f), color-stop(0.3, #22f), color-stop(0.45, #2ff), color-stop(0.6, #2f2),color-stop(0.75, #2f2), color-stop(0.9, #ff2), color-stop(1, #f22) ) !important;
  color: #a41e34 !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  font-size: 50px !important;
  text-align: center !important;
  font-style: normal !important;
  -webkit-animation: colorchange 20s infinite alternate;
}

@-webkit-keyframes colorchange {
  0% {
    color: #a41e34;
  }

  50% {
    color: #a41e34;
  }

  80% {
    color: #1abc9c;
  }

  30% {
    color: #d35400;
  }

  60% {
    color: #a41e34;
  }

  70% {
    color: #34495e;
  }

  80% {
    color: #f1c40f;
  }

  90% {
    color: #2980b9;
  }

  100% {
    color: #a41e34;
  }
}

// .mat-toolbar-row, .mat-toolbar-single-row {
//   //height: 31px;
// }
.horizontal-nav ul .link-item {
  display: inline-block;
}

.horizontal-header .search-form input[type="text"] {
  padding-left: 2.4rem;
  border-radius: 30px 30px 30px 30px;
}
.horizontal-header .search-form input[type="text"] {
  height: 39px;
}
.mat-toolbar.mat-primary {
  background: white !important;
  color: black !important;
}

.mat-icon {
  background-repeat: no-repeat;
  display: inline-block;
  fill: currentColor;
  height: 24px;
  width: 24px;
  // margin-left: -99px;
}

.main-top-menu-row {
  background-color: white;
  border-bottom: 2px solid lightgray;
  position: sticky;
  top: 0px;
  //z-index:99999999;
  z-index: 1000;
}
.horizontal-nav .horizontal-navigation-menu .child-menu {
  width: 100%;
  padding: 1rem 0;
  padding-left: 15px;
  border-radius: 5px;
  background-color: white !important;
  border-top: 2px solid lightgray;
  margin-top: 16px;
}
.horizontal-navigation-menu {
  list-style: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-self: center;
}
.horizontal-navigation-menu > .menu-item {
  font-size: 14px;
  margin: 0px !important;
  padding: 15px 42px;
  outline: none;
}
.child-menu {
  display: none;
  position: absolute;
  background-color: white;
  width: 100%;
  left: 0;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 9999999;
  align-items: flex-start;
  justify-content: flex-start;
  height: 88vh;
  overflow-y: scroll;
}
.menu-item:hover .child-menu {
  display: block;
}

// .category:active + .child-menu {
//   display: none !important;
// }

.active-link:active .child-menu {
  display: none !important;
}

.horizontal-nav ul .link-item {
  display: inline-block;
  font-size: 10px !important;
  padding-top: 1px;
}

.menu-link-item {
  font-size: 16px;
}

.category-heading {
  color: #f26a10;
}

.category {
  color: gray;
}

.category:hover {
  color: black;
}

// .child-menu li:hover {
//   background: #a41e34;
//   color: #fff;
//   transition: 0.4ms ease-in;
//   border-radius: 5px 5px 5px 5px;
//   cursor: pointer;
//   height: 39px !important;
// }

li.ng-start-insterted.text-white {
  background-color: salmon;
}

// .child-menu li  {height: 35px;} /* smaller font size for sub menu items */
// .child-menu li:hover {background:#a41e34; color: white !important;} /* highlights current hovered list item and the parent list items when hovering over sub menues */
// .child-menu ul {
// position:absolute;
// padding:0;
// left:0;
// /*display:none;  hides sublists RK: COMMENTING THIS */
// }
// .child-menu li:hover {display:block;} /* shows sublist on hover */
// .child-menu li li:hover ul {
//   color: white;
// display:block; /* shows sub-sublist on hover */
// margin-left:200px; /* this should be the same width as the parent list item */
// margin-top:-85px; /* aligns top of sub menu with top of list item */
// }

.horizontal-nav ul .link-item1 {
  display: inline-block;
}
.horizontal-nav ul .link-item1 {
  display: inline-block;
  font-size: 13px !important;
  padding: 13px;
}

/* Create three equal columns that floats next to each other */
.child-menu-column {
  font-size: 14px;
  padding: 10px;
  background-color: white;
  float: left;
  width: 11%;
  align-items: flex-start;
  justify-self: flex-start;
  flex-wrap: wrap;
  white-space: normal;
}

.mat-toolbar-row,
.mat-toolbar-single-row {
  // padding: 0px !important;
}
.online-carett {
  border: 1px solid #fff;
  top: 40%;
  height: 16px;
  position: absolute;
  right: 0;
  width: 10px;
  i {
    font-size: 12px;
    line-height: 16px;
    text-align: center;
  }
}
.abc {
  height: 18px;
  width: 18px;
  border: 1px solid #fff;
  position: absolute;
  background-color: #1862c6;
  border-radius: 50%;
  top: 18px;
  left: 24px;
}
.icn {
  color: white;
  margin-left: 3px;
  font-size: 18px;
}

@media screen and (max-width: 600px) {
  .child-menu-column {
    font-size: 14px;
    padding: 10px;
    background-color: white;
    float: left;
    width: 50%;
    align-items: flex-start;
    justify-self: flex-start;
    flex-wrap: wrap;
    white-space: normal;
    overflow-y: scroll !important;
  }
  .mainmenu {
    display: none !important;
  }
  .btndrdn {
    display: none !important;
  }
}
.mat-toolbar-row,
.mat-toolbar-single-row {
  height: 80px;
}

.accordion {
  width: 100%;
  max-width: 360px;
  margin: auto;
  background: #fff;
  border-radius: 4px;
}

.accordion .menu {
  position: relative;
  padding: 15px 15px 15px 45px;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
  transition: all 0.4s ease;
}
.submenu1 .menu {
  position: relative;
  padding: 15px 15px 15px 45px;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
  transition: all 0.4s ease;
}
.submenu2 .menu {
  position: relative;
  padding: 15px 15px 15px 45px;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
  transition: all 0.4s ease;
}

.accordion li:last-child .menu {
  border-bottom: 0;
}

.accordion li i {
  position: absolute;
  top: 1.2rem;
  left: 1rem;
  color: #595959;
  transition: all 0.4s ease;
}

.accordion li i.fa-chevron-down {
  right: 1rem;
  left: auto;
}

.accordion li.active i.fa-chevron-down {
  transform: rotate(180deg);
}

.accordion li.active .menu {
  color: black;
}

.accordion li.active i {
  color: black;
}

/* Show submenu1 */
li.accordion1.active .submenu1 {
  height: 500px;
  overflow: auto;
  font-size: 14px;
  transition: height 0.4s ease;
  margin-bottom: 10px;
  /*
    height: 0 -> height: auto;는 transition이 동작하지 않는다.
    max-height: 임의의 높이;를 지정하면 transition이 동작하지만 타이밍이 망가진다.
    max-height: 1000px;과 max-height: 133px;을 비교해 보라!
    height를 1000px으로 transition할 시간에 실제로는 133px정도만 transition하므로 여는 시간이 닫는 시간보다 빠르다.
  */
  /* max-height: 1000px; */
  /* max-height: 133px; */
}

.submenu1 {
  height: 0;
  overflow: hidden;
  font-size: 14px;
  transition: height 0.4s ease;
  margin-left: 25px;
}
.submenu1 li {
  margin-bottom: 3px;
}
.submenu1 .menu:hover {
  background-color: lightgray;
}

.accordion li:last-child .submenu1 {
  border-radius: 0 0 4px 4px;
}

.accordion li:last-child .submenu1 li:last-child {
  border-bottom: 0;
}

.submenu1 a {
  display: block;
  text-decoration: none;
  color: #d9d9d9;
  padding: 12px;
  padding-left: 42px;
  transition: all 0.25s ease-in-out;
  margin-bottom: 3px;
}

.submenu1 a:hover {
  background: #b63b4d;
  color: #fff;
}

/* Show submenu2 */
li.accordion2.active .submenu2 {
  height: 500px;
  overflow: auto;
  font-size: 14px;
  transition: height 0.4s ease;
  margin-bottom: 10px;
  /*
    height: 0 -> height: auto;는 transition이 동작하지 않는다.
    max-height: 임의의 높이;를 지정하면 transition이 동작하지만 타이밍이 망가진다.
    max-height: 1000px;과 max-height: 133px;을 비교해 보라!
    height를 1000px으로 transition할 시간에 실제로는 133px정도만 transition하므로 여는 시간이 닫는 시간보다 빠르다.
  */
  /* max-height: 1000px; */
  /* max-height: 133px; */
}

.submenu2 {
  height: 0;
  overflow: hidden;
  font-size: 14px;
  transition: height 0.4s ease;
  margin-left: 25px;
}
.submenu2 li {
  margin-bottom: 3px;
}
.submenu2 .menu:hover {
  background-color: lightgray;
}
.accordion li:last-child .submenu2 {
  border-radius: 0 0 4px 4px;
}

.accordion li:last-child .submenu2 li:last-child {
  border-bottom: 0;
}

.submenu2 a {
  display: block;
  text-decoration: none;
  color: #d9d9d9;
  padding: 12px;
  padding-left: 42px;
  transition: all 0.25s ease-in-out;
}

.submenu2 a:hover {
  background: #b63b4d;
  color: #fff;
}
ul {
  list-style-type: none;
}
.accordion2 li.active i.fa-chevron-down {
  transform: rotate(180deg);
}

.qtyIndicator {
  margin-left: 3px;
  color: #f26a10;
}




.global-loader{
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
   background-color: #000;
    z-index: 999999999999;
    opacity: 0.7;
  }
  .innerSpinner
  {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    width: 100px;
    height: 100px;
    background: transparent;
    border-radius: 50%;
    text-align: center;
    line-height: 100px;
    font-family: sans-serif;
  
    font-size: 16px;
  
    color:blue;
  

  
    text-transform: uppercase;
  
    text-shadow: 0 0 10px blue;
  
    box-shadow: 0 0 20px #000000;
  }
  
  .innerSpinner:before
  {
    content: '';
    position: absolute; 
  
    top: -3px;
    left: -3px;
    width: 100%;
    height: 100%;
  
    border: 3px solid transparent;
  
    border-top: 3px solid blue;
    border-right: 3px solid blue;
  
    border-radius: 50%;
  
    animation: animateCircle 2s linear infinite;
  }
  
  .innerSpinner1 
  {
  
    display: block;
    position: absolute;
  
    top: calc(50% - 2px);
    left: 50%;
    width: 50%;
    height: 4px;
    background: transparent;
    transform-origin: left;
    animation: animateCircle1 2s linear infinite;  
  }
  
  .innerSpinner1:before 
  {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #3f51b5;
  
    top: -6px;
    right: -8px;
    box-shadow: 0 0 20px blue;
  }
  
  @keyframes animateCircle 
  {
    0%
    {
      transform: rotate(0deg);
    }
    100%
    {
      transform: rotate(360deg);
    }
  }
  @keyframes animateCircle1
  {
    0%
    {
      transform: rotate(45deg);
    }
    100%
    {
      transform: rotate(405deg);
    }
  }


  .loader-before-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    height: 50px;
    margin-top: 30px;
  }
  .loader-before-loading div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 6px solid #039be5;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #039be5 transparent transparent transparent;
  }
  .loader-before-loading div:nth-child(1) {
    animation-delay: -0.45s;
  }
  .loader-before-loading div:nth-child(2) {
    animation-delay: -0.3s;
  }
  .loader-before-loading div:nth-child(3) {
    animation-delay: -0.15s;
  }
  @keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  #mblNum{
    text-align: center;
  }
  