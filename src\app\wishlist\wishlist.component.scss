.maincontainer{
    max-width: 85%;
    margin: auto;
    padding: 0 10px 16px;
    min-height: 320px;
    color: #282c3f;
}
.secondcontainer{
    width: 25%;
    height: 25vh; // experiment with this value, try changing to 110vh
    min-height: 500px;
    overflow: auto;
    position: -webkit-sticky;
    position: sticky;
    top: 6%;
}

.priceDetails {
    font-size: 12px;
    font-weight: 700;
    margin: 0px 0 16px;
    color: #535766;
    //border-top: 1px solid #eaeaec;
    
}
.brandtitle{
    font-weight: 600;
    text-overflow: ellipsis;
    overflow: hidden;
}
.itemqty{
    font-weight: 600;
}
.detailsBlock{
    font-size: 14px;
    font-family: Whitney,Helvetica,Arial,sans-serif!important;
}
.pricedetailsrow{
    margin-bottom: 12px;
    line-height: 16px;
}
.pricevalue{
   float: right;
}
.order-total {
    font-weight: 600;
    font-size: 15px;
    padding-top: 16px;
    border-top: 1px solid #eaeaec;
    color: #3e4152;
    line-height: 16px;
}
.placeorderbtn{
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px;
    background: #ff3f6c;
    cursor: pointer;
    text-align: center;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.itemheader{
    padding: 18px 12px 18px;
    border: 1px solid #d4d5d9;
}
.itemheader div {
    font-size: 16px;
    font-weight: 600;
    display: inline-block;
}
.totalitem{
    float: right;
}
.itemcontainer{
    background: #fff;
    font-size: 14px;
    border: 1px solid #eaeaec;
    border-radius: 4px;
    position: relative;
    padding: 12px 12px 0;
    cursor: pointer;
}
.itemleftcontainer{
    position: absolute;
    margin-right:10px;
    overflow-wrap: break-word;
}
.itemrightcontainer{
    padding-left: 12px;
    position: relative;
    min-height: 148px;
    margin-left: 111px;
    margin-bottom: 12px;
}
.itemcontainerbase{
     min-height: 97px;
}
.itemcontainerprice{
    position: absolute;
    top: 2px;
    right: 12px;
}

.itemComponents-bold {
    font-weight: 600;
}
.itemComponents-price {
    display: inline-block;
    color: #282c3f;
}
.inlinebtnbrd{
    border-top: 1px solid #eaeaec;
}

.rmvbtn:first-child {
    width: 111px;
}

.rmvbtn {
    line-height: 20px;
}
.rmvbtn {
    display: inline-block;
    width: 49%;
    text-align: center;
    margin: 12px 0;
}
.itemContainer-base-inlineButton {
    font-size: 13px!important;
    letter-spacing: .5px;
    padding: 0;
}

.inlinebuttonV2-base-actionButton {
    text-transform: uppercase;
    font-weight: 600;
    color: #696b79;
    padding: 0;
    height: 30px;
    font-size: 12px;
    text-decoration: none;
    border: none;
    background: none;
    cursor: pointer;
}
.addbtn {
    border-left: 1px solid #d4d5d9;
}

.addbtn2 {
    line-height: 20px;
}
.addbtn {
    display: inline-block;
    width: 20%;
    text-align: center;
    margin: 12px 0;
}
.rightcontainer{
    vertical-align: top;
    display: inline-block;
    width: 100%;
    padding: 28px 16px 26px 16px;
    background-color: white;
    box-shadow: 0 0 35px 0 rgba(154, 161, 171, 0.15);
}
.support{
    font-size: 22px;
    font-weight: 600;
    margin: 0px 0 16px;
    color: #535766;
}
.call{
        font-size: 22px;
        font-weight: 700;
        margin: 0px 0 16px;
        color: #535766;
        text-align: center;
    }
.callmsg{
    font-size: 14px;
    font-weight: 600;
    margin: 0px 0 16px;
    color: #535766;
    text-align: center;
}

.placeorderbtn{
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px;
    cursor: pointer;
    text-align: center;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}
.addButton{
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
    font-size: 14px;
    font-weight: 600;
    padding: 1px;
    cursor: pointer;
    text-align: center;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.removeButton{
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
    font-size: 14px;
    font-weight: 600;
    padding: 1px;
    cursor: pointer;
    text-align: center;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;

   
}