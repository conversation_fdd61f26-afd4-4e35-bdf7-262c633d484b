import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { fabric } from 'app/model/fabric.model';
import { User } from 'app/model/user.model';
import { FileSaverService } from 'ngx-filesaver';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FabricsService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals, private fileSaverService: FileSaverService) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  getAllFabrics(): Observable<fabric[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<fabric[]>(Appconfig.BASE_URL + 'api/techpack/fabric/all', {
      headers: headers
    });
  }

  createFabric(fabric: fabric): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Response>(Appconfig.BASE_URL + 'api/techpack/fabric/', fabric, {
      headers: headers
    });
  }

  updateFabric(fabric: fabric): Observable<Response> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/fabric/', fabric, {
      headers: headers
    });
  }

  activateFabric(fabric: fabric): Observable<Response> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/fabric/' + fabric.uuid + '/activate', fabric, {
      headers: headers
    });
  }

  deactivateFabric(fabricUid: string): Observable<Response> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/fabric/' + fabricUid, {
      headers: headers
    });
  }

  downloadFabrics(){
    let headers = new HttpHeaders({'Content-Type': 'application/csv', 'ptuid':this.globals.tenantUid })
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    this.http.get(Appconfig.BASE_URL+'api/techpack/fabric/import', {
      observe: 'response',
      responseType: 'blob',
      headers:headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, 'fabrics.csv');
    });
  }
}
