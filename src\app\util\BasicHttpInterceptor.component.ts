import { <PERSON><PERSON>p<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { Globals } from '../appconfig/appconfig.component';

@Injectable()
export class BasicHttpInterceptor implements HttpInterceptor {
	constructor(public globals: Globals) { }
	intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
		let user = JSON.parse(localStorage.getItem("userProfile"));
		if (user) {
			request = request.clone({
				setHeaders: {
					// Authorization: this.globals.basicAuth,
					ptuid: user.parentTenantUid
				}
			});
		}
		return next.handle(request);
	}
}
