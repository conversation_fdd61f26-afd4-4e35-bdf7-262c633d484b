<div class="container-fluid" >
    <div class="dropdown show">
        <a class="btn btn-secondary dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown"
            aria-haspopup="true" aria-expanded="false">
            Sort By
        </a>

        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
            <a class="dropdown-item" *ngFor="let item of sortByItems" (click)="sortChanged(item)">{{item}}</a>
        </div>
    </div>
</div>