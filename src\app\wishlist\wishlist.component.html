<div class="ecommerce-product-cart-wrapper maincontainer">
    <div *ngIf="wishlist != undefined && wishlist.length > 0" class="row">
        <div class="col-sm-12 col-md-12 col-lg-12 col-xl-9 mt-4">
            <div class="itemheader">
                <div>Items In Wishlist</div>
                <div class="totalitem">({{wishlist.length}} items)</div>
            </div>
            <div class="itemcontainer" *ngFor="let item of wishlist">
                <div (click)="viewOrderDetailsFromCart(item)">
                    <div class="itemleftcontainer">
                        <a href=""></a>
                        <img src="{{getImg(item)}}" alt="{{item.catalogUid}}" style="width:100px;max-width:100px">
                    </div>
                    <div class="itemrightcontainer">
                        <div *ngIf="item.price || item.netPrice" class="itemcontainerbase">
                            <div class="brandtitle">{{item.catalogUid}} <span *ngIf="item.title">-{{item.title}}</span>
                            </div>
                            <div style="overflow: hidden; text-overflow: ellipsis;">{{item.title}}</div>
                            <div class="itemcontainerprice itemComponents-bold itemComponents-price">
                                <div>
                                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="8" height="10" viewBox="0 0 8 10"
                                        class="itemComponents-base-rupeeBoldIcon">
                                        <path fill-rule="nonzero"
                                            d="M5.841 9.44l-1.21.513L.365 5.968l.547-.875c.251-.005.529-.033.834-.086a4.91 4.91 0 0 0 .896-.242c.292-.11.556-.251.793-.424s.412-.383.526-.63H.564V2.578h3.431a1.779 1.779 0 0 0-.307-.51 1.99 1.99 0 0 0-.52-.426 2.789 2.789 0 0 0-.738-.29 3.955 3.955 0 0 0-.957-.107h-.91V0h6.31v1.244H4.522c.***************.239.29a3.867 3.867 0 0 1 .39.712c.************.109.331h1.613v1.135H5.246a2.51 2.51 0 0 1-.434.906 3.293 3.293 0 0 1-.718.694 3.884 3.884 0 0 1-.916.478 5.378 5.378 0 0 1-1.028.267L5.84 9.44z">
                                        </path>
                                    </svg> --> 
                                    <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                                        {{item.price || 0 | currency:'THB':'symbol-narrow'}}
                                        </ng-container>
                                        <ng-template #otherTenantsCurrency>
                                          {{item.price || 0| currency:tenantConfig.currencySymbol ?
                                            tenantConfig.currencySymbol:'INR'}}
                                        </ng-template>
                                        

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="inlinebtnbrd">
                    <div class="rmvbtn">
                        <button type="button" class="btn btn-danger" (click)="removeWishlist(item)"
                            [disabled]="loading">Remove</button>
                    </div>
                    <div class="addbtn addbtn2">
                        <button type="button" class="btn btn-success" (click)="addToCart(item)" [disabled]="loading">Add
                            to Cart</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12 col-xl-3 secondcontainer">
            <!-- TODO: Implement this <button class="placeorderbtn" type="button" (click)="addToCartAll()">Add all items to Cart</button> -->
        </div>
    </div>
    <div *ngIf="wishlist && wishlist.length == 0">
        <h4 class="mt-5 mb-4 alert alert-success" style="background-color: #dddddd;color: black"> Your wishList is
            empty.
        </h4>
    </div>
    <div *ngIf="wishlist == undefined">
        <h4 class="mt-5 mb-4 alert alert-success" style="background-color: #dddddd;color: black"> Fetching ...
        </h4>
    </div>
</div>