
import { Injectable } from '@angular/core';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import {
  HttpE<PERSON>,
  Http<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest
} from '@angular/common/http';
import { catchError, finalize, map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class HttpsStatusLoaderService {

  private request: BehaviorSubject<boolean>;
  constructor() {
    this.request = new BehaviorSubject(false);
  }

  setHttpStatus(setStatus: boolean) {
    this.request.next(setStatus);
  }

  getHttpStatus(): Observable<boolean> {
    return this.request.asObservable();
  }
}

@Injectable()
export class HTTPListener implements HttpInterceptor {
  constructor(private status: HttpsStatusLoaderService) { }

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    let url = req['url'];
    let url1 =url.substring(url.lastIndexOf('/')+1);
    if(url1 === 'login' || url1 === 'logout'){
      this.status.setHttpStatus(false);
    }else{
      this.status.setHttpStatus(true);
    }

    return next.handle(req).pipe(
      map(event => {
        return event;
      }),
      catchError(error => {
        return throwError(error);
      }),
      finalize(() => {
        this.status.setHttpStatus(false);
      })
    )
  }
}
