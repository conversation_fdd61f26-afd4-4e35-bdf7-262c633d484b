import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CatalogService } from '../../service/catalog.service';

export interface ConfirmationDialogData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  showShipToField?: boolean;
  shipToOptions?: any[];
  customerUid?: string; // Added for API integration
  onSaveNewShipTo?: (newShipTo: any) => void;
}

export interface ShipTo {
  address1: string;
  name?: string; // Optional now
  customerUid?: string;
  shipToId?: string; // Added ship to ID field
}

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss']
})
export class ConfirmationDialogComponent implements OnInit {
  selectedShipTo: any = null;
  expiryDate: string = '';
  remarks: string = '';
  shipToId: string = ''; // Added ship to ID field
  showNewShipToForm: boolean = false;
  isLoadingShipToOptions: boolean = false; // Loading state for API call
  shipToLoadError: string = ''; // Error message for API failures

  // New ship to form data
  newShipTo: ShipTo = {
    address1: '',
    name: 'Ship To Address', // Default name for display in dropdown
    shipToId: '' // Initialize ship to ID
  };

  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogData,
    private catalogService: CatalogService
  ) {}

  ngOnInit(): void {
    // Load ship to options from API if customerUid is provided and showShipToField is true
    if (this.data.showShipToField && this.data.customerUid) {
      this.loadShipToOptions();
    } else if (this.data.shipToOptions && this.data.shipToOptions.length > 0) {
      // Fallback to provided options if no customerUid
      this.selectedShipTo = this.data.shipToOptions[0];
    }

    // No default expiry date - will use placeholder instead
  }

  loadShipToOptions(): void {
    this.isLoadingShipToOptions = true;
    this.shipToLoadError = '';

    this.catalogService.getShipment().subscribe({
      next: (shipToOptions) => {
        this.data.shipToOptions = shipToOptions;
        if (shipToOptions && shipToOptions.length > 0) {
          this.selectedShipTo = shipToOptions[0];
        }
        this.isLoadingShipToOptions = false;
      },
      error: (error) => {
        console.error('Error loading ship to options:', error);
        this.shipToLoadError = 'Failed to load shipping options. Please try again.';
        this.isLoadingShipToOptions = false;
        // Fallback to empty array
        this.data.shipToOptions = [];
      }
    });
  }

  onNoClick(): void {
    this.dialogRef.close(false);
  }

  onYesClick(): void {
    // Format the expiry date to DD-MM-YYYY format if it exists
    let formattedExpiryDate = '';
    if (this.expiryDate) {
      const date = new Date(this.expiryDate);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      formattedExpiryDate = `${day}-${month}-${year}`;
    }

    // Return confirmation, selected shipping address, ship to ID, formatted expiry date, and remarks
    this.dialogRef.close({
      confirmed: true,
      shipTo: this.selectedShipTo,
      shipToId: this.shipToId, // Include the ship to ID
      expiryDate: formattedExpiryDate,
      remarks: this.remarks
    });
  }

  toggleNewShipToForm(): void {
    this.showNewShipToForm = !this.showNewShipToForm;

    // Reset form when opening
    if (this.showNewShipToForm) {
      this.newShipTo = {
        address1: '',
        name: 'Ship To Address' // Default name for display in dropdown
      };
    }
  }

  saveNewShipTo(): void {
    // Validate required fields - only address1 is required now
    if (!this.newShipTo.address1) {
      // You could add error handling here, like showing an error message
      console.error('Please enter a shipping address');
      return;
    }

    // Make sure we have a name for display purposes
    if (!this.newShipTo.name) {
      this.newShipTo.name = 'Ship To Address';
    }

    // Create a copy of the new ship to object
    const newShipToCopy = { ...this.newShipTo };

    // If the parent component provided a callback for saving new ship to addresses, call it
    if (this.data.onSaveNewShipTo) {
      this.data.onSaveNewShipTo(newShipToCopy);
    }

    // Add the new address to the options and select it
    if (!this.data.shipToOptions) {
      this.data.shipToOptions = [];
    }

    this.data.shipToOptions.push(newShipToCopy);
    this.selectedShipTo = newShipToCopy;

    // Close the form
    this.showNewShipToForm = false;
  }
}
