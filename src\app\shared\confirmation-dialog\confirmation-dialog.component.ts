import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CatalogService } from '../../service/catalog.service';

export interface ConfirmationDialogData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  showShipToField?: boolean;
  shipToOptions?: any[];
  customerUid?: string; // Added for API integration
  onSaveNewShipTo?: (newShipTo: any) => void;
}

export interface ShipTo {
  address1: string;
  name?: string; // Optional now
  customerUid?: string;
  shipToId?: string; // Added ship to ID field
}

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss']
})
export class ConfirmationDialogComponent implements OnInit {
  selectedShipTo: any = null;
  expiryDate: string = '';
  remarks: string = '';
  shipToId: string = ''; // Added ship to ID field
  showNewShipToForm: boolean = false;
  isLoadingShipToOptions: boolean = false; // Loading state for API call
  shipToLoadError: string = ''; // Error message for API failures
  isSavingNewShipTo: boolean = false; // Loading state for saving new ship-to address

  // New ship to form data
  newShipTo: ShipTo = {
    address1: '',
    name: 'Ship To Address', // Default name for display in dropdown
    shipToId: '' // Initialize ship to ID
  };

  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogData,
    private catalogService: CatalogService
  ) {}

  ngOnInit(): void {
    // Load ship to options from API if customerUid is provided and showShipToField is true
    if (this.data.showShipToField && this.data.customerUid) {
      this.loadShipToOptions();
    } else if (this.data.shipToOptions && this.data.shipToOptions.length > 0) {
      // Fallback to provided options if no customerUid
      this.selectedShipTo = this.data.shipToOptions[0];
    }

    // No default expiry date - will use placeholder instead
  }

  loadShipToOptions(): void {
    this.isLoadingShipToOptions = true;
    this.shipToLoadError = '';

    this.catalogService.getShipment(this.data.customerUid!).subscribe({
      next: (shipToOptions) => {
        this.data.shipToOptions = shipToOptions;
        if (shipToOptions && shipToOptions.length > 0) {
          this.selectedShipTo = shipToOptions[0];
        }
        this.isLoadingShipToOptions = false;
      },
      error: (error) => {
        console.error('Error loading ship to options:', error);
        this.shipToLoadError = 'Failed to load shipping options. Please try again.';
        this.isLoadingShipToOptions = false;
        // Fallback to empty array
        this.data.shipToOptions = [];
      }
    });
  }

  onNoClick(): void {
    this.dialogRef.close(false);
  }

  onYesClick(): void {
    // Format the expiry date to DD-MM-YYYY format if it exists
    let formattedExpiryDate = '';
    if (this.expiryDate) {
      const date = new Date(this.expiryDate);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      formattedExpiryDate = `${day}-${month}-${year}`;
    }

    // Return confirmation, selected shipping address, ship to ID, formatted expiry date, and remarks
    this.dialogRef.close({
      confirmed: true,
      shipTo: this.selectedShipTo,
      shipToId: this.shipToId, // Include the ship to ID
      expiryDate: formattedExpiryDate,
      remarks: this.remarks
    });
  }

  toggleNewShipToForm(): void {
    this.showNewShipToForm = !this.showNewShipToForm;

    // Reset form when opening
    if (this.showNewShipToForm) {
      this.newShipTo = {
        address1: '',
        name: 'Ship To Address' // Default name for display in dropdown
      };
    }
  }

  saveNewShipTo(): void {
    // Validate required fields - both address and shipToId are required
    if (!this.newShipTo.address1) {
      console.error('Please enter a shipping address');
      return;
    }

    if (!this.newShipTo.shipToId) {
      console.error('Please enter a ship to ID');
      return;
    }

    // Prepare the payload for the API
    const shipToPayload = {
      shipId: this.newShipTo.shipToId,
      shipPartyName: this.newShipTo.address1
    };

    // Call the API to create the new ship-to address
    if (this.data.customerUid) {
      this.isSavingNewShipTo = true; // Start loading state
      this.catalogService.createShipToAddress(this.data.customerUid, shipToPayload).subscribe({
        next: (response) => {
          console.log('Ship-to address created successfully:', response);

          // Immediately call GET API to refresh the ship-to list
          this.catalogService.getShipment(this.data.customerUid!).subscribe({
            next: (updatedShipToOptions) => {
              console.log('Ship-to list refreshed:', updatedShipToOptions);

              // Update the dropdown options with fresh data from API
              this.data.shipToOptions = updatedShipToOptions;

              // Find and select the newly created ship-to address
              // Look for the one that matches our created shipId or shipPartyName
              const newlyCreatedShipTo = updatedShipToOptions.find(option =>
                option.shipId === this.newShipTo.shipToId ||
                option.shipPartyName === this.newShipTo.address1 ||
                option.name === this.newShipTo.address1
              );

              if (newlyCreatedShipTo) {
                this.selectedShipTo = newlyCreatedShipTo;
                console.log('Newly created ship-to address selected:', newlyCreatedShipTo);
              } else if (updatedShipToOptions.length > 0) {
                // Fallback: select the last item (likely the newly created one)
                this.selectedShipTo = updatedShipToOptions[updatedShipToOptions.length - 1];
              }

              // If the parent component provided a callback for saving new ship to addresses, call it
              if (this.data.onSaveNewShipTo && newlyCreatedShipTo) {
                this.data.onSaveNewShipTo(newlyCreatedShipTo);
              }

              // Close the form
              this.showNewShipToForm = false;
              this.isSavingNewShipTo = false; // End loading state
            },
            error: (error) => {
              console.error('Error refreshing ship-to list:', error);
              // Even if refresh fails, close the form as the creation was successful
              this.showNewShipToForm = false;
              this.isSavingNewShipTo = false; // End loading state
            }
          });
        },
        error: (error) => {
          console.error('Error creating ship-to address:', error);
          this.isSavingNewShipTo = false; // End loading state on error
          // You could show an error message to the user here
        }
      });
    } else {
      console.error('Customer UID is required to create ship-to address');
    }
  }
}
