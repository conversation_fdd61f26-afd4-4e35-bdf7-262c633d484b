import { Injectable } from '@angular/core';
import { Subject,Observable,ReplaySubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SizeratioService {

  constructor() { }

 // private sizeQuantity = new Subject<number>();

//   sendQuantity(data){
//     this.sizeQuantity.next(data);

//   }

//   getSizeQty(): Observable<number> {
//     return this.sizeQuantity.asObservable();
// }


private sizeQuantity = new ReplaySubject<any>();
public startedEditing$ = this.sizeQuantity.asObservable();

sendQuantity(data) {
  this.sizeQuantity.next(data);
}
}
