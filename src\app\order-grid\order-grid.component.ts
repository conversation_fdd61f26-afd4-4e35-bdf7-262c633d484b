"use strict";

import { Component, ViewChild, OnInit, ViewEncapsulation, TemplateRef } from '@angular/core';

import { ActivatedRoute, Router } from '@angular/router';

import { Appconfig } from '../appconfig/appconfig.component';

import { Order } from '../model/order.model';
import { OrderItem } from '../model/orderitem.model';
import { OrderSummary } from '../model/OrderSummary.model';
import { Catalog } from '../model/catalog.model';
import { CatalogService } from '../service/catalog.service';
import { OrderService } from '../service/order.service';

import { BsModalService, ModalModule } from 'ngx-bootstrap/modal';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';


@Component({
  selector: 'app-order-grid',
  templateUrl: './order-grid.component.html',
  styleUrls: ['./order-grid.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OrderGridComponent implements OnInit {

  myOrders: OrderSummary[];
  inProgressOrders:OrderSummary[];
  completedOrders:OrderSummary[];
  submittedOrdersNo:number=0;
  tempOrders: OrderSummary[] = [];
  bacRows: OrderSummary[] = [];
  selectedOrder: OrderSummary;

  closeResult: string;
  modalRef: BsModalRef;
  role: string;

  selectedOrderStatus: any = -1;
  currentShowingOrders: string = 'All Orders';

  config = {
    animated: true,
    keyboard: true,
    backdrop: true,
    ignoreBackdropClick: false
  };

  imageUrl: string;
  @ViewChild('deleteBtn ') deleteBtn: TemplateRef<any>;
  @ViewChild('table') table: any;
  userProfile: any;

  //@ViewChild(DatatableComponent) table: DatatableComponent;

  constructor(private route: ActivatedRoute, private catalogService: CatalogService,
    private orderService: OrderService, private modalService: BsModalService, private router: Router) {

  }

  ngOnInit() {
     this.userProfile=JSON.parse(localStorage.getItem("userProfile"));
    this.getOrders();
    this.getInProgressOrders();
    this.getCompletedOrders();

  }

  getOrders() {
    this.orderService.getOrders().subscribe(data => {
      this.myOrders = data.sort((a, b)=> {return b.createdTime - a.createdTime});
      //this.tempOrders = [...data];
      this.bacRows = [...data];
      this.getSubmittedOrderNumber(this.myOrders);
    });
  }

  updateFilter(event) {
    const val = event.target.value;
    if (val == '') {
      this.myOrders = this.bacRows;
      this.getSubmittedOrderNumber(this.myOrders);
      return;
    }
    const temp = this.bacRows.filter(function (d) {
      return d.subCategoryName.toLowerCase().indexOf(val) !== -1 || d.catUid.toLowerCase().indexOf(val) !== -1 || d.subCatUid.toLowerCase().indexOf(val) !== -1 || !val;
    });

    // update the rows
    this.myOrders = temp;
    this.getSubmittedOrderNumber(this.myOrders);
  }

  edit(row, content): void {
    this.selectedOrder = row;
    this.modalRef = this.modalService.show(content, Object.assign({}, this.config, { class: 'dark-modal' }));
  }

  open(content) {
    this.selectedOrder = new OrderSummary();
    this.modalRef = this.modalService.show(content, Object.assign({}, this.config, { class: 'dark-modal' }));
  }

  cancel() {
    this.modalRef.hide();
  }


  refreshOrders() {
    switch (this.selectedOrderStatus) {
      case "1":
        this.getInProgressOrders();
        this.currentShowingOrders = 'In-Progress Orders';
        break;
      case "2":
        this.getCompletedOrders();
        this.currentShowingOrders = 'Completed Orders';
        break;
      default:
        this.getOrders();
        this.currentShowingOrders = 'All Orders';

    }
  }
  public getInProgressOrders(): void {
    this.orderService.getInprogressOrders(this.userProfile.customerUid).subscribe(data => {
      this.inProgressOrders = data;
      //this.tempOrders = [...data];
      this.bacRows = [...data];
    this.getSubmittedOrderNumber(this.myOrders);

    })
  }
  public getCompletedOrders(): void {
    this.orderService.getCompletedOrders(this.userProfile.customerUid).subscribe(data => {
      this.completedOrders = data;
      this.completedOrders.sort((a, b) => b.createdTime - a.createdTime);
      //this.tempOrders = [...data];
      this.bacRows = [...data];
    this.getSubmittedOrderNumber(this.myOrders);

    })
  }

  toggleExpandGroup(group) {
    //  console.log('Toggled Expand Group!', group);
    this.table.groupHeader.toggleExpandGroup(group);
  }

  onDetailToggle(event) {
    //  console.log('Detail Toggled', event);
  }

  displayDate(createdTime) {
    createdTime = new Date(createdTime);
    let date = createdTime.getDate();
    let month = createdTime.toLocaleString('default', { month: 'long' });
    let year = createdTime.getFullYear();
    let hours = createdTime.getHours();
    let minutes = createdTime.getMinutes();
    let ampm = hours >= 12 ? "PM" : "AM";

    hours = hours % 12 || 12;
    minutes = minutes < 10 ? "0" + minutes : minutes;
    createdTime = date +"  "+month +", "+year +" | "+ hours + ":" + minutes + " " + ampm;
    return (createdTime);
  }

  onActivate(event) {
    if (event.type === 'click') {
      this.router.navigate(['/h/productDetails/' + event.row.catalogCode]);
    }

  }

  getProductDetails(code) {
    this.router.navigate(['/h/productDetails/' + code]);
  }

  deactivate(order: Order) {
  }

  public viewOrder(orderUid : number): void {
    this.router.navigate(['/h/orderSummary/' + orderUid]);
  }

  getSubmittedOrderNumber(data){
      this.submittedOrdersNo=0;
      data.forEach(element => {
        if(element.submittedOn > 0){
          this.submittedOrdersNo++;
        }
      });
  }


}

