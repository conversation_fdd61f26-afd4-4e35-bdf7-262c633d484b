{"name": "chankya", "version": "4.0.3", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@amcharts/amcharts4": "^4.10.9", "@angular/animations": "^10.0.1", "@angular/cdk": "^10.0.0", "@angular/common": "^10.0.1", "@angular/compiler": "^10.0.1", "@angular/core": "^10.0.1", "@angular/flex-layout": "^9.0.0-beta.31", "@angular/forms": "^10.0.1", "@angular/localize": "^10.0.1", "@angular/material": "^10.0.0", "@angular/material-moment-adapter": "^9.2.4", "@angular/platform-browser": "^10.0.1", "@angular/platform-browser-dynamic": "^10.0.1", "@angular/platform-server": "^10.0.1", "@angular/router": "^10.0.1", "@egjs/hammerjs": "^2.0.17", "@ks89/angular-modal-gallery": "^7.2.5", "@ng-bootstrap/ng-bootstrap": "^6.2.0", "@ng-select/ng-select": "^4.0.1", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@swimlane/ngx-datatable": "^17.1.0", "@types/hammerjs": "^2.0.36", "angular-calendar": "^0.28.16", "angular-file-uploader": "^6.2.7", "angular-resizable-element": "^3.2.4", "angular-slick-carousel": "^3.1.7", "angular-tree-component": "^8.4.0", "card": "^2.4.0", "classlist.js": "^1.1.20150312", "core-js": "^2.6.9", "d3": "^5.16.0", "date-fns": "^1.30.1", "dragula": "^3.7.2", "easy-pie-chart": "^2.1.7", "element-closest": "^3.0.2", "file-saver": "^2.0.2", "firebase": "^7.15.0", "font-awesome": "^4.7.0", "hammerjs": "^2.0.8", "intl": "^1.2.5", "jquery": "^3.5.1", "jqvmap": "^1.5.1", "leaflet": "^1.5.1", "leaflet-map": "^0.2.1", "magnific-popup": "^1.1.0", "moment": "^2.24.0", "mousetrap": "^1.6.5", "ng-image-slider": "^2.5.0", "ng-lazyload-image": "^8.0.1", "ng-multiselect-dropdown9": "^2.13.0", "ngx-bootstrap": "^5.6.1", "ngx-contextmenu": "^5.4.0", "ngx-drag-scroll": "^9.0.0-beta.2", "ngx-file-drop": "^9.0.1", "ngx-filesaver": "^9.0.0", "ngx-gallery-9": "^1.0.6", "ngx-image-zoom": "^0.6.0", "ngx-infinite-scroll": "^9.0.0", "ngx-perfect-scrollbar": "^9.0.0", "ngx-progressbar": "^6.0.3", "ngx-toastr": "^12.1.0", "ngx-useful-swiper": "^9.0.3", "normalize.css": "^8.0.1", "nvd3": "^1.8.6", "perfect-scrollbar": "^1.4.0", "popper.js": "^1.14.6", "primeicons": "^4.0.0-rc.2", "primeng": "^9.1.0", "promise-polyfill": "8.1.0", "rxjs": "^6.6.0", "rxjs-compat": "^6.6.3", "screenfull": "^5.0.2", "simple-line-icons": "^2.4.1", "skycons": "^1.0.0", "sortablejs": "^1.9.0", "summernote": "^0.8.12", "swiper": "^5.4.5", "tether": "^1.4.6", "ts-helpers": "^1.1.2", "tslib": "^2.0.0", "uninstall": "0.0.0", "web-animations-js": "^2.3.2", "widgster": "^1.0.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1000.8", "@angular/cli": "^10.0.0", "@angular/compiler-cli": "^10.0.1", "@angular/language-service": "^10.0.1", "@types/core-js": "^2.5.0", "@types/d3": "^5.7.2", "@types/jasmine": "^3.3.13", "@types/jquery": "^3.3.29", "@types/mousetrap": "^1.6.3", "@types/node": "^12.12.47", "@types/nvd3": "^1.8.39", "@types/swiper": "^5.4.0", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~3.3.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "rxjs-tslint": "^0.1.7", "ts-loader": "^6.0.2", "ts-node": "^8.2.0", "tslint": "~6.1.0", "typescript": "^3.9.5"}}