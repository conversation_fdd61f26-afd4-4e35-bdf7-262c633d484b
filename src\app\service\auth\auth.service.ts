import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { environment } from '../../../environments/environment';
import { User } from '../../model/user.model';


@Injectable({
   providedIn: 'root'
})
export class AuthService {
   basicAuth = localStorage.getItem("basicAuth");
   userData: any;
   isLoggedIn = false;

   constructor(
      private router: Router,
      private globals: Globals,
      private http: HttpClient,
      private toastr: ToastrService) {
   }

   /*
    *  getLocalStorageUser function is used to get local user profile data.
    */
   getLocalStorageUser() {
      this.userData = JSON.parse(localStorage.getItem("userProfile"));
      if (this.userData) {
         this.isLoggedIn = true;
         return true;
      } else {
         this.isLoggedIn = false;
         return false;
      }
   }

   /*
* signupUserProfile method save email and password into firabse &
* signupUserProfile method save the user sign in data into local storage.
*/
   signupUserProfile(value) {
      // this.firebaseAuth
      //  .createUserWithEmailAndPassword(value.email, value.password)
      //  .then(value => {
      //    this.toastr.success('Account Created!');
      //    this.setLocalUserProfile(value);
      //    this.router.navigate(['/']);
      //  })
      //  .catch(err => {
      //     this.toastr.error(err.message);
      //  });
   }

   login(username: string, password: string, tenantUid: string) {

      var token = 'Basic ' + btoa(username + ':' + password);
      let headers: any;
      headers = new HttpHeaders({ 'Content-Type': 'application/json', "Authorization": token })
      .set("ptuid", tenantUid)

      //.set("Authorization", token);
      return this.http.get<User>(`${environment.apiUrl}/api/user/details`, {
         headers: headers
      }).pipe(map(user => {
         // store user details and jwt token in local storage to keep user logged in between page refreshes
         user.token = token
         localStorage.setItem('currentUser', JSON.stringify(user));
         //this.currentUserSubject.next(user);
         return user;
      }));

   }

   //  generateOtpNum(mobileNumber): Observable<any> {
   //     console.log("tenantUid",mobileNumber);
   //    // let basicAuth = localStorage.getItem('baiscAuth');
   //   // var token = 'Basic ' + btoa(tenantUid);
   //    let headers = new HttpHeaders({'Content-Type': 'application/json',})
   //     //.set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
   //      console.log("headers",headers);
   //    return this.http.post<any>(Appconfig.BASE_URL + 'api/user/otp/stori', mobileNumber, {
   //      headers: headers
   //    });
   //  }

   verifyOtpNum(otpData): Observable<any> {
      const headers = { 'content-type': 'application/json' }
      const body = JSON.stringify(otpData);
      console.log(body)
      return this.http.post<any>(Appconfig.BASE_URL + 'api/user/otp/stori/verify', body, { headers: headers })
   }


   generateOtpNum(mobileNumber): Observable<any> {
      // const headers = { 'content-type': 'application/json'};
      let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      //.set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
      const body = JSON.stringify(mobileNumber);
      console.log(body)
      return this.http.post(Appconfig.BASE_URL + 'api/user/otp/stori', body, { 'headers': headers })
   }

   /*
    * resetPassword is used to reset your password.
    */
   resetPassword(value) {
      // this.firebaseAuth.sendPasswordResetEmail(value.email)
      //    .then(value => {
      //     	this.toastr.success("Email Sent");
      //     	this.router.navigate(['/session/loginone']);
      //    })
      //    .catch(err => {
      //       this.toastr.error(err.message);
      //    });
   }

   /*
    * logOut function is used to sign out .
    */
   logOut() {

      localStorage.removeItem("basicAuth");
      localStorage.removeItem("token");
      localStorage.removeItem("userProfile");
      localStorage.removeItem("currentUser");
      localStorage.removeItem("tenantConfig");
      localStorage.removeItem("customers");
      // localStorage.removeItem("itemData");
      this.isLoggedIn = false;
      this.toastr.success("You have been successfully logged out!");
      this.router.navigate(['/session/loginone']);
   }

   /*
    * setLocalUserProfile function is used to set local user profile data.
    */
   setLocalUserProfile(value) {
      localStorage.setItem("userProfile", JSON.stringify(value));
      this.isLoggedIn = true;
   }
}
