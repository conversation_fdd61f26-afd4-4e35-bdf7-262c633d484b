<div class="box d-flex flex-column product-details-wrapper plr-40" id="product-details-wrapper-id" tabindex="-1">
  <div class="row-fluid d-flex flex-row product-details-wrapper row">
    <div class="p-2 col-xl-8 col-lg-8 col-md-8 col-12 column"
      style="box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;">
      <image-zoom *ngIf="true" [images]="images" [product]="product" [purchasedOrderData]="purchasedOrderData">
      </image-zoom>
    </div>
    <div class="d-flex flex-column col-xl-4 col-lg-4 col-md-4 col-12 column" id="sticky"
      style="box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;padding: 10px;">
      <div class="d-flex flex-column">
        <div class="d-flex flex-column" style="border-bottom: 1px solid lightgray;padding-bottom: 10px;">
          <div class="product-title">{{product.uuid}} <span *ngIf="product.title">-{{product.title}}</span>
          </div>
          <div class="style-name">{{product.description | lowercase}}</div>
        </div>
        <div class="d-flex flex-row margin-top-20 align-content-center" style="align-items: center;color:#ff905a">
          <div *ngIf="isShowAmt" class="product-price mr-3">{{product.price |
            currency:tenantConfig.currencySymbol || 'INR'}}
          </div>
          <!-- <h2 class="margin-right-20"><del>Rs. 899</del></h2>
                    <h2 class="text-warning margin-right-20">(56% OFF)</h2> -->
          <div *ngIf="false" class="d-flex flex-row" style="color: red; cursor: pointer;" (click)="likeProduct()"
            data-toggle="tooltip" data-placement="top" title="Like Product">
            <mat-icon style="font-size: 27px;">favorite</mat-icon>
            <!-- <div style="font-size: 16px;">10</div> -->
          </div>
        </div>

        <!-- <div class="text-success">inclusive of all taxes</div> -->
      </div>

      <size-quantity-shared style="margin-right:8%" [from]="'product'" [productDetails]="sendProductDetails.asObservable()"
        (sizeQuantityData)="getSizeQuantity($event)">
      </size-quantity-shared>
      <div class="row" *ngIf="productColors && productColors.length>0">
        <div class="colours">Colours:</div>
      </div>

      <div class="maincontainer" *ngIf="productColors && productColors.length>0">
        <div class="d-flex flex-wrap flex-row">
          <div class="d-flex flex-column" *ngFor="let color of productColors">
            <img *ngIf="color.url" src="{{color.url}}" class="img-fluid cursor-pointer"
              (click)="viewProductDetails(color.product)">
            <span *ngIf="color.cc" src="{{color.url}}" [style.background]="color.cc"
              (click)="viewProductDetails(color.product)" class="img-fluid cursor-pointer"></span>
          </div>
        </div>
      </div>

      <div class="d-flex flex-row margin-top-20">
        <button (click)="addToCart()" type="button"
          class="btn  btn-warning margin-right-20 d-flex flex-row align-items-center">
          <mat-icon>add_shopping_cart</mat-icon>Add To Cart
        </button>
        <button *ngIf="!product.inWishlist" type="button"
          class="btn btn-primary margin-right-20  d-flex flex-row align-items-center" (click)="addToWatchlist()">
          <mat-icon>turned_in_not</mat-icon>Wishlist
        </button>
        <button *ngIf="product.inWishlist" type="button"
          class="btn btn-primary margin-right-20  d-flex flex-row align-items-center" (click)="removeFromWishlist()">
          <mat-icon>turned_in</mat-icon>Wishlist
        </button>
      </div>
      <div class="d-flex flex-column margin-top-20 product-details">
        <h2>PRODUCT DETAILS</h2>
        <p>{{product.description}}</p>
        <div class="d-flex flex-column margin-top-10 " *ngIf="product.fitType != null">
          <h5>Fit & Closure</h5>
          <p>{{product.fitType}} <span *ngIf="product.closureType">- {{product.closureType}}</span> </p>
        </div>
        <div class="d-flex flex-column margin-top-10">
          <h5>Specifications</h5>
          <!-- <ul class="nav nav-pills nav-justified">
                        <li class="active"><a href="#">Home</a></li>
                        <li><a>Menu 1</a></li>
                        <li><a>Menu 2</a></li>
                        <li><a>Menu 3</a></li>
                    </ul> -->


          <mat-tab-group>
            <mat-tab label="Production Details">
              <ng-template matTabContent>
                <div class="tabPanelWrapper">
                  <div class="specifications-grid">
                    <div class="spec-item" *ngIf="product.fabricComposition">
                      <div class="spec-label">Fabric Composition</div>
                      <!-- <div class="spec-value">{{getDefault(product.fabricComposition)}}</div> -->
                    </div>
                    <div class="spec-item" *ngIf="product.fabricCount">
                      <div class="spec-label">Fabric Count/Cons</div>
                      <div class="spec-value">{{getDefault(product.fabricCount)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.pattern && product.pattern != 'NA'">
                      <div class="spec-label">Pattern</div>
                      <div class="spec-value">{{getDefault(product.pattern)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.fit">
                      <div class="spec-label">Fit</div>
                      <div class="spec-value">{{getDefault(product.fit)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.sleeve">
                      <div class="spec-label">Sleeve</div>
                      <div class="spec-value">{{getDefault(product.sleeve)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.garmentTreatment">
                      <div class="spec-label">Garment Treatment</div>
                      <div class="spec-value">{{getDefault(product.garmentTreatment)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.closureType">
                      <div class="spec-label">Closure Type</div>
                      <div class="spec-value">{{getDefault(product.closureType)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.samplePolicy">
                      <div class="spec-label">Sample Policy</div>
                      <div class="spec-value">{{getDefault(product.samplePolicy)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.exclusivity">
                      <div class="spec-label">Exclusivity</div>
                      <div class="spec-value">{{getDefault(product.exclusivity)}}</div>
                    </div>
                    <div class="spec-item" *ngIf="product.offerMonth">
                      <div class="spec-label">Offer Of Month</div>
                      <div class="spec-value">{{getDefault(product.offerMonth)}}</div>
                    </div>
                  </div>
                </div>
                
              </ng-template>
            </mat-tab>
            <mat-tab label="Composition Details">
              <ng-template matTabContent>
                <div class="tabPanelWrapper">
                  <div class="d-flex flex-row margin-top-10 " *ngIf="product.gender">
                    <div class="spec-border-bottom">
                      <div class="spec-label">Gender</div>
                      <div class="spec-value">{{getDefault(product.gender)}}</div>
                    </div>
                  </div>
                  <div class="d-flex flex-row margin-top-10 " *ngIf="product.color">
                    <div class="spec-border-bottom">
                      <div class="spec-label">Color</div>
                      <div class="spec-value">{{getDefault(product.color)}}</div>
                    </div>
                  </div>
                  <div class="d-flex flex-row margin-top-10 " *ngIf="product.occasion">
                    <div class="spec-border-bottom">
                      <div class="spec-label">Occassion</div>
                      <div class="spec-value">{{getDefault(product.occasion)}}</div>
                    </div>
                  </div>
                  <div class="d-flex flex-row margin-top-10 " *ngIf="product.productStory">
                    <div class="spec-border-bottom">
                      <div class="spec-label">Story</div>
                      <div class="spec-value">{{getDefault(product.productStory)}}</div>
                    </div>
                  </div>
                </div>
              </ng-template>
            </mat-tab>
          </mat-tab-group>
          <!-- <ul class="nav nav-pills" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" aria-expanded="true" data-toggle="tab" href="#tab-1" role="tab"
                                aria-controls="active">
                                Production Details</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" data-toggle="tab" href="#tab-2" role="tab"
                                aria-controls="inactive">Composition Details</a>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link" data-toggle="tab" href="#tab-3" role="tab"
                                aria-controls="inactive">Factory Details</a>
                        </li>
                    </ul> -->

          <!-- <div class="tab-content">


                        <div role="tabpanel" class="tab-pane fade" id="tab-3">
                            <div class="d-flex flex-row margin-top-10 ">
                                <div class="spec-border-bottom">
                                    <div class="spec-label">Vendor</div>
                                    <div class="spec-value">{{getDefault(product.vendorName)}}</div>
                                </div>
                                <div class="spec-border-bottom">
                                    <div class="spec-label">Location</div>
                                    <div class="spec-value">{{getDefault(product.location)}}</div>
                                </div>
                            </div>
                        </div>
                    </div> -->

          <!-- <div class="d-flex flex-row margin-top-10 spec-border-bottom" *ngIf="!isSeeMore"
                        style="width: 100%;">
                        <div class="text-warning cursor-pointer" (click)="seeMore()">See More</div>
                    </div> -->
          <!-- <div *ngIf="isSeeMore" lass="d-flex flex-column margin-top-10 ">
                        <div class="d-flex flex-row margin-top-10 ">
                            <div class="spec-border-bottom">
                                <div class="spec-label">Fabric</div>
                                <div class="spec-value">Cotton</div>
                            </div>
                            <div class="spec-border-bottom">
                                <div class="spec-label">Fabric</div>
                                <div class="spec-value">Cotton</div>
                            </div>
                        </div>
                        <div class="d-flex flex-row margin-top-10 ">
                            <div class="spec-border-bottom">
                                <div class="spec-label">Fabric</div>
                                <div class="spec-value">Cotton</div>
                            </div>
                            <div class="spec-border-bottom">
                                <div class="spec-label">Fabric</div>
                                <div class="spec-value">Cotton</div>
                            </div>
                        </div>
                        <div class="d-flex flex-column margin-top-10 spec-border-bottom" style="width: 100%;">
                            <h4>Complete The Look</h4>
                            <div>Make a name for yourself this season with this cool T-shirt from DILLINGER. A
                                fashionable
                                weekend outfit starts with this navy blue top matched with chinos and your favourite
                                pair of
                                sunglasses to cast an effortlessly cool image.</div>
                        </div>
                    </div> -->
          <!--                     <div class="d-flex flex-column margin-top-10 ">
                        <div class="d-flex flex-row ">
                            <div>Product Id:</div>
                            <h4>{{product.uuid}}</h4>
                        </div>
                        <div class="d-flex flex-row ">
                            <div>Sold by:</div>
                            <h4>ARVIND</h4>
                        </div>
                    </div> -->

        </div>
      </div>
    </div>
  </div>
  <div *ngIf="tenantConfig.enableRecomm != null && tenantConfig.enableRecomm == '1'">
    <div class="d-flex flex-column p-3">
      <h3>SIMILAR PRODUCTS</h3>
      <div class="col-12">
        <horizontal-gallery type="similar" [productCategory]="product.uuid"></horizontal-gallery>
      </div>
    </div>
    <div class="d-flex flex-column p-3">
      <h3>CUSTOMERS ALSO LIKED</h3>
      <div class="col-12">
        <horizontal-gallery type="like" [productCategory]="product.uuid"></horizontal-gallery>
      </div>
    </div>
    <div class="d-flex flex-column p-3">
      <h3>CUSTOMERS ALSO BOUGHT</h3>
      <div class="col-12">
        <horizontal-gallery type="bought" [productCategory]="product.uuid"></horizontal-gallery>
      </div>
    </div>
  </div>

</div>

<div class="buttons-wrapper">
  <div class="next-prev-buttons">
    <div class="previous-button" (click)="previousItem()" *ngIf="productItems.length > 0">
      <span class="material-icons arrow-size">
        keyboard_arrow_left
      </span>
    </div>
    <div class="next-button" (click)="nextItem()" *ngIf="productItems.length > 0">
      <span class="material-icons arrow-size">
        keyboard_arrow_right
      </span>
    </div>
  </div>


  <div class="go-back-listing" *ngIf="product">
    <button type="button" class="btn btn-primary" (click)="goBack()" title="Back to category"><i
        class="fa fa-arrow-left" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
  </div>

</div>


<div class="global-loader" *ngIf="isLoading">
  <div class="innerSpinner">
    LOADING...
    <span class="innerSpinner1"></span>
  </div>
</div>
