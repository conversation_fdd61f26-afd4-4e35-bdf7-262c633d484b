<div class="row" style="position: relative;">
    <div class="images_wrapper" *ngFor="let eachImage of images" style="box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;padding: 15px;">
      <div class="images">
        <button *ngIf="purchasedOrderData && purchasedOrderData.length > 0" (click)="openModal(template)" class="btn btn-outline purchased-order-view-button" type="button">
          <i class="fa fa-desktop"></i> You purchased this item already.
          <button class="btn btn-outline-dark">View Order</button>
        </button>
  
        <lib-ngx-image-zoom [thumbImage]="eachImage.url" [fullImage]="eachImage.url"></lib-ngx-image-zoom>
        <img class="new-badge" src="assets/img/new_flag.png" alt="newFlag" *ngIf="product.isNew && product.isNew == 1">
      </div>
    </div>
  </div>
<!-- 
<ng-template #template>
    <div class="modal-header">
        <h4 class="modal-title pull-left">{{product.uuid}}-{{product.title}}</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="zoom-image-display d-flex flex-row">

            <div class="zoom-image-thumbnails-wrapper">
                <div *ngFor="let eachImage of images" class="d-flex flex-column">
                    <div class="eachImage-thumbnail"
                        [class.activeDisplayingImage]="currentViewImag.url == eachImage.url"
                        (click)="viewImage(eachImage)">
                        <img src="{{eachImage.url}}" width="100px" height="100px">
                    </div>
                </div>
            </div>
            <div class="diplaying-image">
                <img src="{{currentViewImag.url}}" width="100%" height="100%">
            </div>

        </div>
    </div>
</ng-template> -->


<ng-template #template>
    <div class="modal-header">
        <b>
            <h4 class="modal-title pull-left">{{product.uuid}}</h4>
        </b>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="zoom-image-display d-flex flex-row">
            <div class="container">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Size</th>
                            <th>Qty Ordered</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of purchasedOrderData">
                            <td>{{item.sizeCode}}</td>
                            <td>{{item.qty}}</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>Total Qty: {{getTotalQty()}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</ng-template>