<div class="chart-wrapper">
  <div class="row">
    <mat-form-field appearance="outline" style="margin-left: 50px;">
      <mat-label>Choose summary option</mat-label>
      <mat-select [(ngModel)]="chartMode" (ngModelChange)="selectMode($event)">
        <mat-option value="OPT">Options</mat-option>
        <mat-option value="QTY">Qty</mat-option>
        <mat-option value="AMT">MRP</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-chip-list style="margin: 18px;">
      <mat-chip *ngIf="selectedBrand != ''" [selectable]="true" [removable]="true" (removed)="removeBrand()">
        Sub-brand:&nbsp;&nbsp;<b>{{selectedBrand}}</b>&nbsp;&nbsp;
        <mat-icon matChipRemove>cancel</mat-icon>
      </mat-chip>
      <mat-chip *ngIf="selectedCategory != ''" [selectable]="true" [removable]="true" (removed)="removeCategory()">
        Category:&nbsp;&nbsp;<b>{{selectedCategory}}</b>&nbsp;&nbsp;
        <mat-icon matChipRemove>cancel</mat-icon>
      </mat-chip>
    </mat-chip-list>
  </div>


  <div class="row">
    <div class="col-xl-4 col-lg-12 col-md-12 col-12" id="chartdiv" style="width: 30%; height: 400px;"></div>
    <div class="col-xl-4 col-lg-12 col-md-12 col-12" id="categoryChartDiv" style="width: 50%; height: 400px;"></div>
    <div class="col-xl-4 col-lg-12 col-md-12 col-12" id="categoryRatingChartDiv" style="width: 20%; height: 400px;">
    </div>
  </div>
</div>

<div class="ecommerce-product-cart-wrapper maincontainer">
  <div *ngIf="items != undefined && items.length > 0" class="row">
    <div class="col-sm-12 col-md-12 col-lg-12 col-xl-9 mt-4">
      <div class="itemheader">
        <div>Items In Your Cart</div>
        <div>
          <input type="text" class="form-control itemcartinpt" [(ngModel)]="searchStyleCodeText"
            placeholder="Search by style code">
        </div>
        <div class="totalitem">({{items.length}} items)</div>
      </div>
      <div class="itemcontainer" *ngFor="let item of items  | cartSearchbyStyleCode : searchStyleCodeText"
        [hidden]="!canDisplay(item)">
        <div>
          <div (click)="viewOrderDetailsFromCart(item)"
            style="display: flex;flex-direction: row;align-items:center;width:100%">
            <div class="itemleftcontainer">
              <a href=""></a>
              <img src="{{getImg(item)}}" alt="{{item.catalogUid}}" style="width:100px">
            </div>
            <div class="itemrightcontainer">
              <div class="itemcontainerbase">
                <div class="brandtitle">{{item.catalogUid}} <span *ngIf="item.title">-{{item.title}}</span></div>
                <div>{{item.title}}</div>
                <div class="itemqty"><span style="display:block">Qty:
                    {{item.qty}}</span><span style="display:block">Options:
                    {{item.options}}</span><span style="display:block">Rating:
                    {{item.rating}}</span>
                  <!--size-quantity-shared [product]="product" readonly="true">
                </size-quantity-shared-->
                </div>
                <div class="itemcontainerprice itemComponents-bold itemComponents-price">
                  <div *ngIf="isShowAmt">

                      <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                      {{item.price || 0 | currency:'THB':'symbol-narrow'}}
                      </ng-container>
                      <ng-template #otherTenantsCurrency>
                        {{item.price || 0|currency:tenantConfig.currencySymbol ?
                          tenantConfig.currencySymbol:'INR'}}
                      </ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="inlinebtnbrd">
            <div class="rmvbtn">
              <button (click)="removeItem(item)"
                class="inlinebuttonV2-base-actionButton itemContainer-base-inlineButton removeButton">Remove</button>
            </div>
            <div class="addbtn addbtn2">
              <button class="inlinebuttonV2-base-actionButton itemContainer-base-inlineButton removeButton"
                type="button" (click)="editItem(item)">Edit</button>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="col-sm-12 col-md-12 col-lg-12 col-xl-3 secondcontainer">
      <!--new-->
      <div style="margin-bottom: 10px;" class="rightcontainer">
        <div class="priceDetails d-block">
          DETAILS
        </div>
        <div class="detailsBlock">
          <div class="pricedetailsrow">
            <span>Total Pieces</span>
            <span class="pricevalue">{{totalCount}}</span>
          </div>
          <div class="pricedetailsrow">
            <span>Total Options</span>
            <span class="pricevalue">{{totalOptions}}</span>
          </div>
          <div *ngIf="isShowAmt" class="pricedetailsrow">
            <span>Order Total</span>
            <span class="pricevalue">
              <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                <strong>{{totalValue || 0 | currency:'THB':'symbol-narrow'}}</strong>
                </ng-container>
                <ng-template #otherTenantsCurrency>
                  <strong>{{ totalValue || 0 | currency:tenantConfig.currencySymbol ?tenantConfig.currencySymbol:'INR' }}</strong>
                </ng-template></span>
          </div>
        </div>
        <div *ngIf="isShowAmt" class="order-total">
          <span>Total</span>
          <span class="pricevalue">
            <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
              <strong>{{totalValue || 0 | currency:'THB':'symbol-narrow'}}</strong>
              </ng-container>
              <ng-template #otherTenantsCurrency>
                <strong>{{ totalValue || 0 | currency:tenantConfig.currencySymbol ?tenantConfig.currencySymbol:'INR' }}</strong>
              </ng-template></span>
        </div>
        <div class="mt-3">
          <a>
            <div class="row">
              <div *ngIf="tenantConfig?.saveToDraft" class="placeorderbtn" style="background-color:#065b28"
                (click)="sendReport()">SAVE ORDER
              </div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <div class="placeorderbtn" (click)="preSubmit()">PLACE ORDER</div>
            </div>
          </a>
        </div>
      </div>

      <div class="rightcontainer">
        <div class="support">Support</div>
        <div class="call"><i class="fa fa-phone"></i> +91 ************</div>
        <div class="callmsg">Please contact with us if you have any questions. We are available 24/7</div>
      </div>

    </div>
  </div>
  <div *ngIf="items == undefined || items.length == 0">
    <h4 class="mt-5 mb-4 alert alert-success" style="background-color: #dddddd;color: black"> Your cart is empty.
    </h4>
  </div>

</div>
