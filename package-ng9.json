{"name": "chankya", "version": "4.0.3", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@amcharts/amcharts4": "^4.9.26", "@angular/animations": "^9.1.10", "@angular/cdk": "^9.2.4", "@angular/common": "^9.1.10", "@angular/compiler": "^9.1.10", "@angular/core": "^9.1.10", "@angular/flex-layout": "^9.0.0-beta.31", "@angular/forms": "^9.1.11", "@angular/localize": "^9.1.10", "@angular/material": "^9.2.4", "@angular/material-moment-adapter": "^9.2.4", "@angular/platform-browser": "^9.1.10", "@angular/platform-browser-dynamic": "^9.1.10", "@angular/platform-server": "^9.1.10", "@angular/router": "^9.1.10", "@ng-select/ng-select": "^4.0.1", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@swimlane/ngx-datatable": "^17.0.0", "angular-calendar": "^0.28.16", "angular-file-uploader": "^6.2.7", "angular-resizable-element": "^3.2.4", "angular-tree-component": "^8.4.0", "card": "^2.4.0", "classlist.js": "^1.1.20150312", "core-js": "^2.6.9", "d3": "^5.16.0", "date-fns": "^1.30.1", "dragula": "^3.7.2", "easy-pie-chart": "^2.1.7", "element-closest": "^3.0.2", "file-saver": "^2.0.2", "firebase": "^7.15.0", "font-awesome": "^4.7.0", "hammerjs": "^2.0.8", "intl": "^1.2.5", "jquery": "^3.4.1", "jqvmap": "^1.5.1", "leaflet": "^1.5.1", "leaflet-map": "^0.2.1", "magnific-popup": "^1.1.0", "moment": "^2.24.0", "ng-multiselect-dropdown9": "^2.13.0", "ngx-bootstrap": "^5.6.1", "ngx-contextmenu": "^5.4.0", "ngx-file-drop": "^9.0.1", "ngx-filesaver": "^9.0.0", "ngx-gallery-9": "^1.0.6", "ngx-image-zoom": "^0.6.0", "ngx-perfect-scrollbar": "^9.0.0", "ngx-sortablejs": "^3.0.0", "ngx-toastr": "^12.1.0", "normalize.css": "^8.0.1", "nvd3": "^1.8.6", "perfect-scrollbar": "^1.4.0", "popper.js": "^1.14.6", "primeicons": "^4.0.0-rc.2", "primeng": "^9.1.0", "promise-polyfill": "8.1.0", "rxjs": "^6.5.5", "rxjs-compat": "^6.5.2", "screenfull": "^5.0.2", "simple-line-icons": "^2.4.1", "skycons": "^1.0.0", "slick-carousel": "^1.8.1", "sortablejs": "^1.9.0", "summernote": "^0.8.12", "tether": "^1.4.6", "ts-helpers": "^1.1.2", "tslib": "^1.10.0", "web-animations-js": "^2.3.2", "widgster": "^1.0.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.901.7", "@angular/cli": "^9.1.7", "@angular/compiler-cli": "^9.1.10", "@angular/language-service": "^9.1.10", "@types/core-js": "^2.5.0", "@types/d3": "^5.7.2", "@types/jasmine": "^3.3.13", "@types/jquery": "^3.3.29", "@types/node": "^12.12.47", "@types/nvd3": "^1.8.39", "codelyzer": "^5.1.2", "jasmine-core": "^3.4.0", "jasmine-spec-reporter": "^4.2.1", "karma": "^4.1.0", "karma-chrome-launcher": "^2.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "^2.0.5", "karma-jasmine": "^2.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "^5.4.2", "rxjs-tslint": "^0.1.7", "ts-loader": "^6.0.2", "ts-node": "^8.2.0", "tslint": "^5.17.0", "typescript": "^3.8.3"}}