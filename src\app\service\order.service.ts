import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { Order } from 'app/model/order.model';
import { OrderDetails } from 'app/model/OrderDetails.model';
import { OrderSummary } from 'app/model/OrderSummary.model';
import { PaymentInfo } from 'app/model/PaymentInfo.model';
import { SizeRatio } from 'app/model/SizeRatio.model';
import { Status } from 'app/model/status.model';
import { User } from 'app/model/user.model';
import { saveAs } from 'file-saver';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

import { environment } from '../../environments/environment';
import { OrderItem } from '../model/orderitem.model';

@Injectable({
  providedIn: 'root'
})
export class OrderService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));
  $cartCount: BehaviorSubject<number> = new BehaviorSubject(0);
  $wishListCount: BehaviorSubject<number> = new BehaviorSubject(0);

  constructor(private httpin: HttpClient,
    private globals: Globals,) {
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
    this.http = httpin;
  }


  public getOrders(): Observable<OrderSummary[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderSummary[]>(`${environment.apiUrl}/api/order/`, {
      headers: headers
    });
  }

  public getOrdersSummary(orderUid: number): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<any[]>(`${environment.apiUrl}/api/order/${orderUid}/items`, {
      headers: headers
    });
  }

  public getCompletedOrders(custUid: string): Observable<OrderSummary[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderSummary[]>(`${environment.apiUrl}/api/order/completed/${custUid}/cust`, {
      headers: headers
    });
  }

  public getInprogressOrders(custUid: string): Observable<OrderSummary[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderSummary[]>(`${environment.apiUrl}/api/order/inprogress/${custUid}/cust`, {
      headers: headers
    });
  }


  saveOrder(order: Order): Observable<Status> {
    let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Status>(Appconfig.BASE_URL + '/catalog/order/', order, {
      headers: headers
    });
  }

  public getUserProfile(): Observable<any> {
    const userProfile = localStorage.getItem("userProfile");
    return of(JSON.parse(userProfile));
  }

  public saveOrUpdateOrder(details: OrderDetails): Observable<Status> {
    // // let basicAuth = localStorage.getItem('baiscAuth');
    // let customerUid = user.customerUid;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/order/`, details, {
      headers: headers
    }).pipe(
      tap(data => {
        setTimeout(() => {
          this.getCartSummary().subscribe(data => {
            if (data.length > 0) {
              let cartCount = 0;
              for (let i of data) {
                cartCount = parseInt(cartCount + '') + parseInt(i.options + '');
              }
              this.$cartCount.next(cartCount);
            } else {
              this.$cartCount.next(0);
            }
          });
        }, 500);
      })
    );


    // return this.getUserProfile().pipe(
    //   switchMap(user => {
    //     let customerUid = user.customerUid;
    //     let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
    //       .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    //       return this.http.put<Status>(`${environment.apiUrl}/api/order/${customerUid}`, details, {
    //         headers: headers
    //       }).pipe(
    //         tap(data => {
    //           setTimeout(() => {
    //             this.getCartSummary().subscribe(data => {
    //               if (data.length > 0) {
    //                 let cartCount = 0;
    //                 for (let i of data) {
    //                   cartCount = parseInt(cartCount + '') + parseInt(i.options + '');
    //                 }
    //                 this.$cartCount.next(cartCount);
    //               } else {
    //                 this.$cartCount.next(0);
    //               }
    //             });
    //           }, 500);
    //         })
    //       );
    //   })
    // );


  }

  public saveOrUpdateOrderSync(details: OrderDetails): Observable<Status> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/order/sync/`, details, {
      headers: headers
    });
  }

  public addOrderItems(items: OrderItem[]): Observable<Status> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/order/items`, items, {
      headers: headers
    });
  }

  public getOrderItems(): Observable<OrderItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderItem[]>(`${environment.apiUrl}/api/order/items`, {
      headers: headers
    });
  }

  public getCartSummary(): Observable<OrderItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderItem[]>(`${environment.apiUrl}/api/order/cart`, {
      headers: headers
    }).pipe(
      tap(data => {
        if (data.length > 0) {
          let cartCount = 0;
          for (let i of data) {
            cartCount = parseInt(cartCount + '') + parseInt(i.options + '');
          }
          this.$cartCount.next(cartCount);
        } else {
          this.$cartCount.next(0);
        }
      })
    );
  }

  public sendReport(): Observable<Status> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Status>(`${environment.apiUrl}/api/order/getreport`, {
      headers: headers
    });
  }

  public getOrderItemsByCatalogUid(catalogUid: string, status: number): Observable<OrderItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderItem[]>(`${environment.apiUrl}/api/order/items/${catalogUid}/status/${status}`, {
      headers: headers
    });
  }

  public deleteOrderItem(items: OrderItem[]): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/order/items`, {
      headers: headers
    });
  }

  public deleteOrderItemByCatalogUid(catUid: string): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Status>(`${environment.apiUrl}/api/order/items/${catUid}`, {
      headers: headers
    });
  }


  getPendingOrder(): Observable<Order> {
    let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Order>(Appconfig.BASE_URL + '/catalog/order/', {
      headers: headers
    });
  }

  submitPendingOrder(expiryDate?: string, remarks?: string, shipToId?: string, shipToName?: string, showShipTo?: boolean): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    // Create payload with conditional ship-to fields
    const payload: any = {
      expiryDate: expiryDate || "",
      remarks: remarks || ""
    };

    // Only include shipId and shipName if showShipTo is true
    if (showShipTo) {
      payload.shipId = shipToId || "";
      payload.shipName = shipToName || "";
    }

    // Use the customer UID in the format {customerUid} for the API endpoint
    let customerIdentifier = `${this.user.customerUid}`;
    return this.http.put<Status>(`${environment.apiUrl}/api/order/${customerIdentifier}/submit`, payload, {
      headers: headers
    }).pipe(
      tap(() => {
        this.getCartSummary().subscribe();
      })
    );
  }

  getOrder(uuid: string): Observable<Order> {
    let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Order>(Appconfig.BASE_URL + '/catalog/order/' + uuid, {
      headers: headers
    });
  }

  getOrdersByStatus(status: number): Observable<OrderSummary[]> {
    let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderSummary[]>(`${environment.apiUrl}/api/order/`, {
      headers: headers
    });
  }


  public getSizeRatio(): Observable<SizeRatio[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<SizeRatio[]>(`${environment.apiUrl}/api/order/SizeRatio`, {
      headers: headers
    });
  }

  public getSizeRatioBySubcat(subcat: string): Observable<SizeRatio[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<SizeRatio[]>(`${environment.apiUrl}/api/order/sizeRatio/${subcat}/subcat`, {
      headers: headers
    });
  }

  public updateSizeRatio(ratios: SizeRatio[]): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Status>(`${environment.apiUrl}/api/order/sizeRatio`, ratios, {
      headers: headers
    });
  }

  public deleteSizeRatio(ratios: SizeRatio[]): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Status>(`${environment.apiUrl}/api/order/sizeRatio`, {
      headers: headers
    });
  }

  getOrderItemsByStatus(status: number): Observable<OrderItem[]> {
    let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderItem[]>(Appconfig.BASE_URL + '/catalog/orderitem/' + status + '/status', {
      headers: headers
    });
  }

  updateOrderStatus(order: Order): Observable<Status> {
    let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(Appconfig.BASE_URL + '/catalog/order/' + status + '/status', order, {
      headers: headers
    });
  }

  public initPayment(): Observable<PaymentInfo> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<PaymentInfo>(Appconfig.BASE_URL + 'api/payment/', {}, {
      headers: headers
    });
  }


  public verifyPayment(payment: PaymentInfo): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(Appconfig.BASE_URL + 'api/payment/', payment, {
      headers: headers
    });
  }

  downloadOrderAsExcel(orderUid: String) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' }).set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    var HTTPOptions = {
      headers: headers,
      'responseType': 'text'
    }
    this.http.get(Appconfig.BASE_URL + 'api/order/getexcel/' + orderUid + '/0', {
      headers: headers,
      'responseType': 'text'
    })
      .toPromise()
      .then(response =>
        this.saveToFileSystem(response)
      );
  }

  saveToFileSystem(response) {
    const filename = "orderitems.csv";//parts[1].split('=')[1];
    const blob = new Blob([response], { type: 'text/plain' });
    saveAs(blob, filename);
  }

  getWishListItems() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<any[]>(`${environment.apiUrl}/api/order/wishlist`, {
      headers: headers
    }).pipe(
      tap(data => {
        let wishlistCount = data.length;
        this.$wishListCount.next(wishlistCount);
      })
    );
  }

  saveWishListItem(wishlist) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put(`${environment.apiUrl}/api/order/wishlist`, wishlist, {
      headers: headers
    }).pipe(
      tap(data => {
        this.getWishListItems().subscribe();
      })
    );
  }
  removeWishListItem(wishlist) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put(`${environment.apiUrl}/api/order/wishlist`, wishlist, {
      headers: headers
    });
  }

}
