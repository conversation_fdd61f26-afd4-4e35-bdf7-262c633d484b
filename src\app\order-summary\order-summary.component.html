<div class="ecommerce-product-cart-wrapper maincontainer">
  <div class="row">
    <div class="col-sm-12 col-md-12 col-lg-12 mt-4">
      <div *ngIf="!isOrderDetailSelected" class="container" style="margin-top: 10px;">
        <div class="btn-group" style="display: flex; justify-content: space-around;">
          <button type="button" class="btn btn-lg btn-outline-primary" (click)="populateData('category')"
            [ngClass]="selectedTab=='category'? 'selectedButton' : ''">Category</button>
          <button type="button" class="btn btn-lg btn-outline-primary" (click)="populateData('subBrand')"
            [ngClass]="selectedTab=='subBrand'? 'selectedButton' : ''">Sub-brand</button>
          <button type="button" class="btn btn-lg btn-outline-primary" (click)="populateData('rating')"
            [ngClass]="selectedTab=='rating'? 'selectedButton' : ''">Rating</button>
        </div>
        <table style="border: 1px solid; margin-top: 1%;" class="table table-striped table-bordered table-hover">
          <thead>
            <tr>
              <th scope="col">Name</th>
              <th scope="col">Options</th>
              <th scope="col">Units</th>
              <th scope="col">Quantity</th>
              <th scope="col">MRP</th>
              <th scope="col">Net Price</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let order of orderSummary">
              <td>
                {{order?.subCatUid}}
              </td>
              <td>
                {{order?.options}}
              </td>
              <td>
                {{order?.unit}}
              </td>
              <td>
                {{order?.qty}}
              </td>
              <td>

                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                  {{order?.price | currency:'THB':'symbol-narrow'}}
                </ng-container>
                <ng-template #otherTenantsCurrency>
                  {{order?.price | currency:tenantConfig.currencySymbol ? tenantConfig.currencySymbol : 'INR'}}
                </ng-template>

              </td>
              <td>
                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency1">
                  {{order?.netPrice | currency:'THB':'symbol-narrow'}}
                </ng-container>
                <ng-template #otherTenantsCurrency1>
                  {{order?.netPrice | currency:tenantConfig.currencySymbol ? tenantConfig.currencySymbol : 'INR'}}
                </ng-template>
              </td>
              <td>
                <button *ngIf="selectedTab=='category'" (click)="prepareDataForStyleCode(order)" type="button"
                  class="btn btn-primary"><i class="fa fa-eye"></i></button>
                <button *ngIf="selectedTab=='subBrand'" (click)="prepareDataForStyleCode(order)" type="button"
                  class="btn btn-primary"><i class="fa fa-eye"></i></button>
                <button *ngIf="selectedTab=='rating'" (click)="prepareDataForStyleCodeRating(order)" type="button"
                  class="btn btn-primary"><i class="fa fa-eye"></i></button>
              </td>
            </tr>
            <tr>
              <td> Total</td>
              <td>{{calculateSumForOptions()}}</td>
              <td>{{calculateSumForUnit()}}</td>
              <td>{{calculateSumForQty()}}</td>
              <td>
                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                  {{calculateSumForPrice() | currency:'THB':'symbol-narrow'}}
                </ng-container>
                <ng-template #otherTenantsCurrency>
                  {{calculateSumForPrice() | currency:tenantConfig.currencySymbol ? tenantConfig.currencySymbol : 'INR'}}
                </ng-template>
              </td>
              <td>
                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                  {{calculateSumForNetPrice() | currency:'THB':'symbol-narrow'}}
                </ng-container>
                <ng-template #otherTenantsCurrency>
                  {{calculateSumForNetPrice() | currency:tenantConfig.currencySymbol ? tenantConfig.currencySymbol : 'INR'}}
                </ng-template>
              </td>
              <td></td>
            </tr>
          </tbody>
        </table>
        <div class="back-button" *ngIf="orderSummary">
          <button type="button" class="btn btn-primary" (click)="goBack('orderSummary')" title="Back"><i
              class="fa fa-arrow-left" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
        </div>
      </div>
      <div *ngIf="isOrderDetailSelected" class="container">
        <div class="order-detail-card" *ngFor="let item of orderDetails"
          (click)="getPurchaseOrderDetailPopUp(item, template)">
          <img class="flex-item" [src]='item.image' [alt]='item.styleCode' style="width: 20%;">
          <div class="flex-item">
            <div class="card-style-code">{{item.styleCode}}</div>
            <div>Quantity: {{item.qty}}</div>
            <div>Unit: {{item.unit}}</div>
            <div>Rating:{{item.rating}}</div>
            <div><b>
                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                  {{item.price | currency:'THB':'symbol-narrow'}}
                  </ng-container>
                  <ng-template #otherTenantsCurrency>
                    Total MRP: {{item.price |
                      currency:tenantConfig.currencySymbol ? tenantConfig.currencySymbol:'INR'}}
                  </ng-template></b></div>
            <div><b>
                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency1">
                  {{item.netPrice | currency:'THB':'symbol-narrow'}}
                  </ng-container>
                  <ng-template #otherTenantsCurrency1>
                    Total NET: {{item.netPrice |
                      currency:tenantConfig.currencySymbol ? tenantConfig.currencySymbol:'INR'}}
                  </ng-template></b></div>
          </div>
          <div class="icon-div">
            <i class="fa fa-angle-double-right"></i>
          </div>
        </div>
        <div class="back-button" *ngIf="orderDetails">
          <button type="button" class="btn btn-primary" (click)="goBack('orderDetail')" title="Back"><i
              class="fa fa-arrow-left" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
        </div>
      </div>
    </div>
  </div>
</div>


<ng-template #template>
  <div class="modal-header">
    <b>
      <h4 class="modal-title pull-left">{{purchasedOrderDetail[0]?.styleCode}}</h4>
    </b>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="zoom-image-display d-flex flex-row">
      <div class="container">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Size</th>
              <th>Qty Ordered</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of purchasedOrderDetail">
              <td>{{item?.sizeCode}}</td>
              <td>{{item?.qty}}</td>
            </tr>
            <tr>
              <td></td>
              <td>Total Qty: {{getTotalQty()}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-template>
