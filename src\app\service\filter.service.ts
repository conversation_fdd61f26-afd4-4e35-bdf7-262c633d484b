import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FilterService {
  backupFilter: any;

  constructor() { }

  public menuChanges = new Subject<boolean>();

  sendClickEvent(status) {
    this.menuChanges.next(status);
  }

  public saveBackupFilter(filterData): void {
    this.backupFilter = filterData;
  }

  public getBackUpFilterData(): any {
    return this.backupFilter;
  }
}
