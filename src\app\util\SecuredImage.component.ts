import { Component, Input, OnChanges } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable, BehaviorSubject} from 'rxjs';
import 'rxjs/add/operator/switchMap';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

// <secured-image [src]="image.images.original.url"></secured-image>
@Component({
  selector: 'secured-image',
  template: `
    
  `
})
export class SecuredImageComponent implements OnChanges  {

  @Input() private src: string;
  private src$ = new BehaviorSubject(this.src);
  ngOnChanges(): void {
    this.src$.next(this.src);
  }

/*
  // this stream will contain the actual url that our img tag will load
  // everytime the src changes, the previous call would be canceled and the
  // new resource would be loaded
 dataUrl$ = this.src$.switchMap(url => this.loadImage(url))
  
  // we need HttpClient to load the image and DomSanitizer to trust the url
  constructor(private httpClient: HttpClient, private domSanitizer: DomSanitizer) {
  }

  private loadImage(url: string): Observable<any> {
    return this.httpClient
      .get(url, {responseType: 'blob'})
      .map(e => this.domSanitizer.bypassSecurityTrustUrl(URL.createObjectURL(e)))
  } */
}
