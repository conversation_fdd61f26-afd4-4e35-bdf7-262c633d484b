<!--  <drag-scroll style="width: 100px; height: 200px">
    Big text goes here...
  </drag-scroll>   -->





<!-- 
<ks-carousel [id]="100" [images]="firstRowImages"
  [carouselConfig]="{maxWidth: '100%', maxHeight: '90%', showArrows: true, objectFit: 'cover', keyboardEnable: true, modalGalleryEnable: false, legacyIE11Mode: false}"
  [previewConfig]="{visible: false, number: 5, width: 'auto', maxHeight: '87vh'}"
  [carouselImageConfig]="{description: {style: {position:'absolute',top:'50%'}}}">
</ks-carousel>
<br /> -->

<ngb-carousel *ngIf="firstRowImages" style="width: 100%;height:90vh;" id="section1">
  <ng-template ngbSlide *ngFor="let item of firstRowImages">
    <img [src]="item.img" alt="{{item.name}}" class='carouselImage'>
    <div class="carousel-caption">
      <span class="carousel-title">{{item.name}}</span>
    </div>
  </ng-template>

</ngb-carousel>

<!-- <ngb-carousel *ngIf="thirdRowImages" style="width: 100%; height:90vh;" id="section2">
  <ng-template ngbSlide *ngFor="let item of thirdRowImages">
    <img [src]="item.image" alt="{{item.title}}" class='carouselImage'>
    <div class="carousel-caption">
      <span class="carousel-title">{{item.title}}</span>
    </div>

  </ng-template>

</ngb-carousel> -->

<!-- <ngb-carousel *ngIf="secondRowImages" style="width: 100%; height:90vh;" id="section3">
  <ng-template ngbSlide *ngFor="let item of secondRowImages">
    <img [src]="item.img" alt="{{item.description}}" class='carouselImage'>
    <div class="carousel-caption">
      <span class="carousel-title">{{item.description}}</span>
    </div>
  </ng-template>

</ngb-carousel> -->