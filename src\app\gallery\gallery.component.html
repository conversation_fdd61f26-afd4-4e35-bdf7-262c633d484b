<div class="container-fluid p-1 plr-40">
  <div class="row">
    <!-- <div class="col-2 sticky-filter">
            <catalog-filter></catalog-filter>
        </div> -->
    <div class="col-12">
      <div class="d-flex flex-column">

        <div class="stickyListHeader">
          <div class="d-flex flex-column">
            <div class="d-flex flex-row align-items-center justify-content-between">
              <div class="d-flex flex-row align-items-center">
                <div *ngIf="true" class="filterButton" style="margin-right: auto; display: flex; flex-direction: row;"
                  (click)="toggleFilter()">
                  <i class="fa fa-filter" aria-hidden="true" style="color:gray;"></i><span
                    class="filterText">Filter</span>
                </div>
                <input type="text" class="form-control" style="margin-left: 10px;" [(ngModel)]="searchStyleCodeText"
                  placeholder="Search by style code,title, description">

              </div>
              <div class="d-flex flex-row align-items-center justify-content-center">
                <gallery-sort *ngIf="false" (change)="sortChanged($event)"></gallery-sort>
                <!-- <button type="button"
                                    class="btn btn-primary sz">
                                    <mat-icon>stars</mat-icon>
                                </button> -->
                <mat-icon title="Size Ratio" style="color: gray;cursor: pointer;margin-right: 10px;"
                  (click)="openSizeratioModal(sizeRatioTemplate)">favorite_border
                </mat-icon>
                <!-- <button type="button" mat-mini-fab color="basic"
                                    >

                                </button> -->

                <div (click)="toggleGrid()" style="cursor: pointer; color:gray;margin-left: 10px;">
                  <i *ngIf="!smallerGrid" class="fa fa-th gridicon" style="font-size: 35px;" aria-hidden="true"
                    data-toggle="tooltip" title="Small Grid"></i>
                  <i *ngIf="smallerGrid" class="fa fa-th-large gridicon" style="font-size: 35px;" data-toggle="tooltip"
                    title="Large Grid"></i>
                  &nbsp;&nbsp;
                </div>
              </div>
            </div>
            <div *ngIf="true" [hidden]="hideStickyFilter" class="stickyFilter">
              <div class="col-12">
                <catalog-filter (closeCatalogFilter)="closeFilter($event)" (onFilter)=filterChanged($event)>
                </catalog-filter>
              </div>
            </div>
          </div>
        </div>



        <div class="d-flex flex-wrap align-items-start justify-content-between search-results" infinite-scroll
          [infiniteScrollDistance]="ngxInfiniteScrollConfig.scrollDistance"
          [infiniteScrollUpDistance]="ngxInfiniteScrollConfig.scrollUpDistance"
          [infiniteScrollThrottle]="ngxInfiniteScrollConfig.throttle">

          <!-- <div > -->
          <ng-container class="p-1"
            *ngFor="let product of productItems | searchbyStyleCode : searchStyleCodeText ; let productIndex = index">
            <div  *ngIf = "disableProduct(product)" style="margin: 20px;padding: 3px;" class="card glry overCard" [title]="prepareTitle(product)" [style.width.px]="smallerGrid ? 250 : 400">
              <div class="d-flex flex-column">
                <!-- <carousel [isAnimated]="true" [showIndicators]="false" [noPause]="false">
                                    <slide *ngFor="let imageUrl of product.thumbnailImages">
                                        <img [defaultImage]="" [lazyLoad]="imageUrl" alt="{{product.title}}"
                                            style="display: block; width: 210px; height:280px;">
                                    </slide>
                                </carousel> -->

                <ngb-carousel #carousel (click)="viewDetails(product)" [interval]="false" data-interval="false"
                  [pauseOnHover]="false" [showNavigationArrows]="false" (mouseenter)="mouseEnterOnCarousel(carousel)"
                  (mouseleave)="mouseLeaveOnCarousel(carousel)" [style.height.px]="smallerGrid ? 249 : 400"
                  class="each_corousel">
                  <ng-template ngbSlide *ngFor="let imageUrl of getThumbnailUrls(product);">
                    <a rel="nofollow noopener noreferrer">
                      <div class="picsum-img-wrapper" *ngIf="getImgUrl(imageUrl)">
                        <div>
                          <img class="imgsmallscrwth" class="background-image" [defaultImage]=""
                            [lazyLoad]="getImgUrl(imageUrl)" alt="{{product.title}}" style="display: block;"
                            [style.height.px]="smallerGrid ? 249 : 400" [alt]="product.uuid">
                        </div>
                        <div>
                          <img class="new-badge" src="assets\img\new_flag.png" alt="newFlag"
                            *ngIf="product.isNew && product.isNew ==1">
                        </div>
                      </div>
                      <div [style.height.px]="smallerGrid ? 249 : 400" [attr.id]="product.uuid"
                        *ngIf="!getImgUrl(imageUrl)" style="color:white;text-align: center;">
                        Image not loading
                      </div>
                    </a>
                  </ng-template>
                </ngb-carousel>

                <div class="d-flex flex-column p-3">
                  <div class="product-title">
                    <strong [class.isProductTitleCenter]="!product.title">{{product.uuid}}</strong><br>
                    <span
                        *ngIf="product.title">
                        {{product.title}}
                        </span>
                  </div>
                  <div class="product-description">
                    <span>{{product.description | lowercase}}</span>
                  </div>
                  <div
                    style="display: flex;flex-direction: row;align-items: center;justify-content:space-between; margin-top: 7px;">
                    <div class="product-price" style="margin-right: 10%;">
                      <div style="display:block" *ngIf ="product.price">
                        <span>MRP</span> &nbsp;
                        <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                        {{product.price | currency:'THB':'symbol-narrow'}}
                        </ng-container>
                        <ng-template #otherTenantsCurrency>
                          {{ product.price | currency:tenantConfig.currencySymbol ?tenantConfig.currencySymbol:'INR' }}
                        </ng-template>
                      </div>
                     <div style="display:block" *ngIf ="product.netPrice" [ngClass]="{ 'disabled': !product.price }">
                        <span>NET</span> &nbsp;
                        <ng-container *ngIf="tenantConfig.currencySymbol === 'THB'; else otherTenantsCurrency1">
                          {{ product.netPrice | currency:'THB':'symbol-narrow' }}
                        </ng-container>
                        <ng-template #otherTenantsCurrency1>
                          {{ product.netPrice | currency:tenantConfig.currencySymbol ?tenantConfig.currencySymbol:'INR' }}
                        </ng-template>
                      
                     </div>
                    </div>
                    <div class="showIconsOnhover">
                      <div title="Wishlist" *ngIf="!product.inWishlist" (click)="addToWatchlist(product)"
                        class="d-flex flex-row wishlist_button"
                        style="color:#17a2b8;cursor: pointer; margin-right: 7px;">
                        <mat-icon>turned_in_not</mat-icon>
                      </div>
                      <div title="Item alredy added to wishlist" *ngIf="product.inWishlist"
                        (click)="removeFromWishlist(product)" class="d-flex flex-row wishlist_button"
                        style="color:#17a2b8;cursor: pointer; margin-right: 7px;">
                        <mat-icon>turned_in</mat-icon>
                      </div>

                      <div *ngIf="!product.inCart" title="Add to cart"
                        (click)="openAddToCartModal(addToCartTemplate, product)" class="d-flex flex-row"
                        style="color:#ff3e6c; cursor: pointer; margin-left: 7px;">
                        <mat-icon>add_shopping_cart</mat-icon>
                      </div>
                      <div *ngIf="product.inCart" (click)="removeFromCart(product)" class="d-flex flex-row"
                        title="Item already added to cart" (click)="viewDetails(product)" style="color:green; cursor: pointer; margin-left: 7px;">
                        <mat-icon>add_shopping_cart</mat-icon>
                      </div>

                      <i *ngIf="product.likeCount" class="fa fa-heart-o p-2" aria-hidden="true"
                        style="color: red;"><span style="padding: 2px;"
                          class="text-dark">{{formatCount(product.likeCount)}}</span></i>
                      <i *ngIf="product.viewCount" class="fa fa-eye text-primary p-2" aria-hidden="true"><span
                          style="padding: 2px;" class="text-dark">{{formatCount(product.viewCount)}}</span></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- <div class="images">
                            <img src="/assets/img/galleryV2/1.jpg" width="100%" height="100%">
                            <div class="d-flex flex-row image-count">
                                <mat-icon>favorite</mat-icon>
                                <div>10</div>
                            </div>
                            <div class="d-flex flex-row image-trending">
                                <div>Trending</div>
                            </div>
                        </div> -->

          <!-- <ngx-slick-carousel class="carousel" #slickModal="slick-carousel" [config]="slideConfig"
                            (init)="slickInit($event)" (breakpoint)="breakpoint($event)" (afterChange)="afterChange($event)"
                            (beforeChange)="beforeChange($event)">
                            <div ngxSlickItem *ngFor="let slide of slides" class="slide">
                                <img src="{{ slide.img }}" alt="" width="100%">
                            </div>
                        </ngx-slick-carousel>

                        <button (click)="addSlide()">Add</button>
                        <button (click)="removeSlide()">Remove</button>
                        <button (click)="slickModal.slickGoTo(2)">slickGoto 2</button>
                        <button (click)="slickModal.unslick()">unslick</button> -->

          <!-- </div> -->
        </div>
        <div *ngIf="!showZipResponse && productItems && productItems.length == 0" class="no-products">
          No Products Found
        </div>
        <div *ngIf="showZipResponse" class="no-products">
          Fetching Products...
        </div>

      </div>
    </div>
  </div>
</div>

<ng-template #sizeRatioTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Define Size Ratios</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body" style="overflow-x: scroll;">
    <app-size-ratio-dialog [cat]="cat" [subcat]="subcat" (saveEvent)="ratioSaveEvent($event)">
    </app-size-ratio-dialog>
  </div>
  <!--     <div class="modal-footer">
        <button type="button" (click)="save()" class="btn btn-primary">Save</button>
        <button type="button" (click)="modalRef.hide()" class="btn btn-secondary" data-dismiss="modal">Close</button>
    </div>   -->

</ng-template>

<ng-template #addToCartTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Add to Cart<span *ngIf="currentProduct && currentProduct.code"> -
        {{currentProduct.code}}</span></h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="currentProduct =null;modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-addcart-details [productDetails]="sendProductDetails.asObservable()"
      (sizeQuantityData)="getSizeQuantity($event)">
    </app-addcart-details>
  </div>
  <div class="modal-footer">
    <button type="button" (click)="addToCart()" class="btn btn-primary">Save</button>
    <button type="button" (click)="close()" class="btn btn-secondary" data-dismiss="modal">Close</button>
  </div>

</ng-template>











































<!--
<div class="gallery-wrapper" style="z-index: 0 !important">
    <div class="lazy1" style="z-index: -1 !important">
        <div class="search-results" style="z-index: 0 !important" infiniteScroll [infiniteScrollDistance]="1"
            [infiniteScrollUpDistance]="2" [infiniteScrollThrottle]="300">
            <div class="row" style="z-index:-1 !important;">
                <div class="col-xs-12 col-sm-4 col-md-3 col-lg-3 col-xl-3 gallery-item"
                    *ngFor="let item of productItems;index as i">

                    <div
                        *ngIf="item.collectionUid != undefined && item.collectionUid != null && ( (paramCollection.uuid == null || paramCollection.uuid == undefined) || (paramCollection != null && paramCollection.userUid == currentUser.uuid ) )   ">
                        <a (click)="unpin(item)" class="text-muted"><i
                                style="color: #c43455; position: absolute; right: 10px; z-index: 999; top: 10px;"
                                class="fa fa-thumb-tack" title="Pin"></i></a>
                        <span *ngIf="bucket != 0 " class="small"
                            style=" position: absolute; right: 10px; z-index: 999; top: 30px;"><span
                                class="fa fa-thumbs-up" style="color: green;"></span> {{item.likeCount}}</span>
                    </div>
                    <div
                        *ngIf="!( item.collectionUid != undefined && item.collectionUid != null && ( (paramCollection.uuid == null || paramCollection.uuid == undefined) || (paramCollection != null && paramCollection.userUid == currentUser.uuid ) ) )  && role != 'VENDOR' ">
                        <a href="javascript:void(0)" id="navbarDropdownMenu2" data-toggle="dropdown"
                            data-keyboard="false" data-backdrop="static" aria-haspopup="true" aria-expanded="false"> <i
                                class="fa fa-thumb-tack" title="Pin"
                                style="color: grey; position: absolute; right: 10px; z-index: 999; top: 10px;"></i></a>
                        <span *ngIf="item.isNew == '1'" class="newProduct"
                            style="position: absolute;right: 10px; z-index: 999; top: 50px;"> New </span>
                        <span *ngIf="bucket != 0 " class="small"
                            style=" position: absolute; right: 10px; z-index: 999; top: 30px;"><span
                                class="fa fa-thumbs-up" style="color: green;"></span> {{item.likeCount}}</span>
                        <div class="dropdown-menu rounded-0 dropdown-menu-lg p-0"
                            style="min-width: 120px; border-radius: 0 0 5px 5px; margin-left: 18%; margin-top: 32px;"
                            data-backdrop="static" data-keyboard="false" aria-labelledby="navbarDropdownMenu2">

                            <div class="dropdown-header bg-normal text-white d-flex justify-content-between"
                                style="padding: 10px;"><span class="align-self-center">Pin to collection</span></div>

                            <div class="form-inline p-2">
                                <input type="text" class="pl-1" placeholder="Select Collection" auto-complete
                                    [(ngModel)]="selectedCollectionName" [source]="collectionNamess"
                                    (valueChanged)="valueChanged($event)" />
                            </div>

                            <div class="form-inline p-2">
                                <input type="text" placeholder="New Collection Name" class="pl-1"
                                    [(ngModel)]="newCollectionName" />
                            </div>

                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-dark">Close</button>
                                <button type="button" class="btn btn-primary"
                                    (click)="addToCollection(item)">Save</button>
                            </div>
                        </div>
                    </div>


                    <div id="img{{i}}" class="grid-thumb">
                        <div *ngIf="item.status==99" class="item-sold">{{item.title}}<br />SOLD</div>
                        <div *ngIf="item.status==3  " class="item-label-sold">

                            <div class="d-flex align-items-start flex-column">
                                <p
                                    style="margin-top: 0;    font-size: 12px;height2: 10px !important;margin-bottom: 0 !important">
                                    {{item.title}}</p>
                                <div class="d-block">
                                    <div class="d-inline-block mr-5"><span
                                            class="small">&nbsp;&nbsp;{{item.compliance}}</span></div>
                                    <div class="d-inline-block mr-3">
                                    </div><span style="position: relative;">SOLD</span>
                                </div>
                            </div>



                        </div>
                        <div *ngIf="item.status==1 || item.status==2 " class="item-label-blocked">

                            <div class="d-block text-left">
                                <p
                                    style="margin-top: 0;    font-size: 12px;height2: 10px !important;margin-bottom: 0 !important; font-size: 13px;">
                                    {{item.title}}</p>
                                <div class="d-block">
                                    <div class="d-inline-block mr-5"><span
                                            class="small">&nbsp;&nbsp;{{item.compliance}}</span></div>
                                    <span *ngIf="item.status==2 && item.addedToCartByUserUid == currentUser.uuid"
                                        style="position: relative;">ADDED TO CART</span>
                                    <span *ngIf="item.status==2 && item.addedToCartByUserUid != currentUser.uuid"
                                        style="position: relative;">BLOCKED</span>
                                    <span *ngIf="item.status==1 " style="position: relative;">BLOCKED</span>

                                </div>
                            </div>
                        </div>


                        <a (click)="openItem(item, i)">
                            <img [lazyLoad]="imageUrl+item.thumbnailImageUid"
                                class="test img-fluid rounded w-100 center-block" style="margin: 0 auto;" />
                        </a>
                        <div *ngIf="item.status==0" class="d-flex justify-content-between thumb-caption">
                            <div class="d-flex align-items-start flex-column">
                                <p
                                    style="margin-top: 0;    font-size: 12px;height2: 10px !important;margin-bottom: 0 !important">
                                    {{item.title}}</p>
                                <div class="d-block">
                                    <div class="d-inline-block mr-5"><span class="small"
                                            style="margin-left: 10;">&nbsp;&nbsp;{{item.compliance}}</span></div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div> -->