import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { fit } from 'app/model/fit.model';
import { User } from 'app/model/user.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FitsService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals:Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  getAllFits(): Observable<fit[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<fit[]>( Appconfig.BASE_URL+'api/techpack/fit/all', {
        headers:headers
      });
}


createFit(fit: fit): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');
  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.post<Response>(Appconfig.BASE_URL + 'api/techpack/fit/', fit, {
    headers: headers
  });
}

updateFit(fit: fit): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/fit/', fit, {
    headers: headers
  });
}

deactivateFit(fitUid: string): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
    .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/fit/' + fitUid, {
    headers: headers
  });
}

activateFit(fit: fit): Observable<Response> {
  // let basicAuth = localStorage.getItem('baiscAuth');

  let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
  .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

  return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/trim/'+ fit.uuid +'/activate', fit, {
    headers: headers
  });
}

}
