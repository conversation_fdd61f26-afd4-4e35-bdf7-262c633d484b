import { Component, OnInit, Input } from '@angular/core';
import { CatalogFilter } from 'app/model/CatalogFilter.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { CatalogService } from 'app/service/catalog.service';
import { Catalog } from 'app/model/catalog.model';
import { NgbCarousel } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';

@Component({
  selector: 'horizontal-gallery',
  templateUrl: './horizontal-gallery.component.html',
  styleUrls: ['./horizontal-gallery.component.scss']
})
export class HorizontalGalleryComponent implements OnInit {

  ngxInfiniteScrollConfig = {
    throttle: 300,
    scrollDistance: 1,
    scrollUpDistance: 2,
  }

  @Input('type') type: string = "like"
  _productCategory: string;
  @Input('productCategory') set productCategory(subcat: string) {
    this._productCategory = subcat;
    if (this._productCategory != undefined) {
      this.prepareDetailsAndGetProducts();
    }
  }

  productItems = [];

  catalogFilter: CatalogFilter;
  tenantConfig: TenantConfig;
  isShowAmt: boolean = true;

  constructor(
    private catalogService: CatalogService,
    private router: Router,
  ) { }

  ngOnInit(): void {
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    if(this.tenantConfig.disableAmt != null && this.tenantConfig.disableAmt != undefined && this.tenantConfig.disableAmt==1){
      this.isShowAmt = false
    }
  }

  prepareDetailsAndGetProducts(): void {
    this.catalogFilter = new CatalogFilter();
    this.catalogFilter.subCatUids = [];
    this.catalogFilter.subCatUids.push(this._productCategory);
    this.catalogFilter.count = 5;
    this.getItems();
  }

  getItems() {

    if (this.type == "like") {
      this.catalogService.getLiked(this._productCategory).subscribe(data => {
        this.productItems = data;
      });
    } else if (this.type == "similar") {
      this.catalogService.getSimilar(this._productCategory).subscribe(data => {
        this.productItems = data;
      });
    } else if (this.type == "bought") {
      this.catalogService.getBought(this._productCategory).subscribe(data => {
        this.productItems = data;
      });
    }
  }

  getImgUrl(url: string) {
    if (this.tenantConfig.imageBaseUrl == undefined || this.tenantConfig.imageBaseUrl == null)
      return url
    else if (this.tenantConfig.imageBaseUrl.slice(-1) == '/') {
      return this.tenantConfig.imageBaseUrl + url;
    } else {
      return this.tenantConfig.imageBaseUrl + '/' + url;
    }
  }


  getThumbnailUrls(item: Catalog): string[] {
    if (item.thumbnailImageUid != null && item.thumbnailImageUid.length > 0) {
      return item.thumbnailImageUid.split(",")
    }
    if (item.imageUid != null && item.imageUid.length > 0) {
      return item.imageUid.split(",")
    }
    return []
  }

  mouseEnterOnCarousel(element: NgbCarousel) {
    element.interval = 500;
    element.cycle();
  }

  mouseLeaveOnCarousel(element: NgbCarousel) {
    element.select('1');
    element.pause()
  }

  viewDetails(product: Catalog) {
    this.router.navigate(['/h/prodDet/' + product.uuid]);
  }

  formatCount(count) {
    if (count == 0 || count == undefined || count == null) {
      return '';
    }
    return count;
  }

}
