export class CatalogFilter {

    price: number ;
    count: number;
    page: number;
    offerMonths: string[];
    productType: string;
    subCategoryType: string;
    isStorySelected: boolean;
    displayedStories: any[];

    trim: string[];
    gender: string[];
    compliance: string[];
    fit: string[];
    leadTime: number;
    offerMonth: string;
    fromPrice: number;
    toPrice: number;
    fabric: string[];
    category: string[];
    wash: string[];
    stories: string[];
    productTypes: string[];
    categoryTypes: string[];
    subCategoryTypes: string[];
    statusCodes: string[];
    fromRow: number;
    pageSize: number;
    sortBy: string;
    categoryType: string;
    subProductType: string;
    bucket: string;
    collectionUid: string;
    status: number;
    searchTerm: string;
    season: string;
    vendorUid: string;
    vendorUids: string[];
    subCatUids: string[];
    catUids: string[];
    brandUids: string[];

    catalogMode: string;

}
