import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { User } from 'app/model/user.model';
import { wash } from 'app/model/wash.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class WashesService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }

  getAllWashes(): Observable<wash[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<wash[]>(Appconfig.BASE_URL + 'api/techpack/wash/all', {
      headers: headers
    });
  }

  createWash(wash: wash): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Response>(Appconfig.BASE_URL + 'api/techpack/wash/', wash, {
      headers: headers
    });
  }

  updateWash(wash: wash): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/wash/', wash, {
      headers: headers
    });
  }

  deactivateWash(washUid: string): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/wash/' + washUid, {
      headers: headers
    });
  }

  activateWash(wash: wash): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' , 'ptuid':this.globals.tenantUid })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/wash/' + wash.uuid + '/activate', wash, {
      headers: headers
    });
  }



}
