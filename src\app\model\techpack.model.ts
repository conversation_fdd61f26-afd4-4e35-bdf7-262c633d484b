export class techpack {
    brandName: string;
    brandUid: string;
    categoryName: string;
    categoryUid: string;
    code: string;
    comments: string;
    createdBy: string;
    createdOn: string;
    createdTime: number;
    deleted: number;
    deliveryUid: string;
    description: string;
    fitUid: string;
    id: number;
    instructions: string;
    modifiedBy: string;
    modifiedOn: string;
    modifiedTime: number;
    name: string;
    options: [
      {
        catalogUid: string;
        colorCode: string;
        createdBy: string;
        createdOn: string;
        createdTime: number;
        deleted: number;
        id: number;
        modifiedBy: string;
        modifiedOn: string;
        modifiedTime: number;
        techpackUid: string;
        tenantId: number;
        tenantUid: string;
        trimFabricCode: string;
        uuid: string
      }
    ];
    performance: string;
    protoStatus: number;
    refId: string;
    remarks: string;
    seasonUid: string;
    status: number;
    storyUid: string;
    styleCodes: string;
    styleName: string;
    tenantId: number;
    tenantUid: string;
    uuid: string;
    washUid: string
}
