<!doctype html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <title>Order Management</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">

  <meta name="author" content="">
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
  <link href="./assets//fonts/google/google_fonts_300to900i.css" rel="stylesheet">
  <link href="./assets/fonts/google/google_material_fonts.css" rel="stylesheet">
  <script src="https://cdn.polyfill.io/v2/polyfill.js?features=Intl.~locale.en"></script>
  <script src="https://unpkg.com/card@2.3.0/dist/card.js"></script>
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  <script src="https://player.twitch.tv/js/embed/v1.js"></script>

  <!-- Global site tag (gtag.js) - Google Analytics -->
  <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=G-E0N8P3KDYQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-E0N8P3KDYQ');
  </script> -->


  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
  <!-- 
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"> -->
  <link href="./assets/fonts/google/google_font_300_500_display_swap.css" rel="stylesheet">
</head>

<body style="overflow-y: hidden;">
  <chankya-app>
    <!-- Page Loader -->
    <div class="block">
      <div class="loading">
        <span class="ball1"></span>
        <span class="ball2"></span>
      </div>
    </div>
    <!-- Page loader Closed -->
  </chankya-app>
  <script type="text/javascript">
    (function (window) {
      // Polyfills DOM4 MouseEvent only on IE11
      // Fixes ngx-charts line-chart hover
      // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/MouseEvent#Polyfill
      if (!!window.MSInputMethodContext && !!document.documentMode || document.documentMode === 10) {
        function MouseEvent(eventType, params) {
          params = params || { bubbles: false, cancelable: false };
          var mouseEvent = document.createEvent('MouseEvent');
          mouseEvent.initMouseEvent(eventType, params.bubbles, params.cancelable, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);

          return mouseEvent;
        }
        MouseEvent.prototype = Event.prototype;
        window.MouseEvent = MouseEvent;
      }
    })(window);
  </script>
</body>

</html>