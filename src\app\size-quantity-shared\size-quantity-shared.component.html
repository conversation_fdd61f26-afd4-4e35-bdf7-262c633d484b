<div class="size-quantity-wrapper">
  <div class="d-flex  flex-column">
    <div class="d-flex flex-column">
      <!-- <form> -->
      <h3>{{product.code}}</h3>
      <div class="rating rating-wrapper-mobile" style="margin-bottom: 15px; margin-top: 15px;">

        <!-- <div class="d-flex flex-row rating-div" style="margin-bottom: 15px;">
          <label class="p-1 pr-1 pt-1 mr-1 lable-font label-width50" for="usr">Rating:</label>
          <input type="text" maxlength="1" class="form-control" id="usr" (input)="updateData('rating')"
            [(ngModel)]="rating" name="rating" placeholder="Enter rating">
        </div> -->

        <div class="d-flex flex-row " *ngIf="sizeCodeUnit.length > 0">
          <label class="p-1 pr-1 pt-1 mr-1  lable-font label-width50" for="usr">Select Unit:</label>
          <select class="browser-default custom-select" (change)="loadForm($event)" [(ngModel)]="selectedSizeUnit">
            <option *ngFor="let sizeunit of sizeCodeUnit" [value]="sizeunit"> {{sizeunit}}</option>
          </select>
        </div>

      </div>
      <div class="form-group" *ngIf="validRatings != null && validRatings.length > 0">
        <label for="exampleSelect1  lable-font">Select Rating</label>
        <select class="layout-item custom-select mt-2 form-control mt-1 rounded-0" [(ngModel)]=dropdownRating
          (ngModelChange)="dropdownRatingChange()">
          <option value="" selected="selected">Select Rating</option>
          <option *ngFor="let rating of validRatings" [value]="rating">{{ rating }}</option>
        </select>
      </div>
      <div class="d-flex flex-row align-content-center input-flex-wrap">
        <mat-spinner *ngIf="qtyData.length==0 && isLoading" diameter="40"></mat-spinner>
        <div *ngIf="qtyData.length>0" class="d-flex  flex-column">
          <div class="p-1" style="width: 85px;font-weight: 600;">Size Ratio</div>
          <input type="text" class="form-control" name="sizeRatio" (keyup)="populateSizeRatio($event)" length="1"
            size="1" maxlength="1" [(ngModel)]="sizeRatio">
        </div> <!-- [disabled]="disabledSizeQuantity" -->
        <div *ngFor="let code of qtyData; let j = index" class="d-flex flex-column  ml-2 ">
          <div class="p-1" *ngIf="showIfStockExist(code)">{{sizeCodes[j]}}</div>
          <input *ngIf="!readonly && showIfStockExist(code)" type="text"
            oninput="this.value=this.value.replace(/(?![0-9])./gmi,'')" maxlength="4" (keypress)="resetSizeRatioField()"
            autofocus="on" style="width:60px;" class="form-control" (input)="inputUpdate()" maxlength="4" size="4"
            name="size{{sizeCodes[j]}}" [(ngModel)]="code.qty" id="{{sizeCodes[j]}}" [readonly]='checkQuantity(code)'>
          <label *ngIf="readonly">{{code.qty}}</label> <!--  [disabled]="disabledSizeQuantity" -->
        </div>
      </div>
      <div *ngIf="stockCheckSum.length > 0" class="d-flex flex-row align-content-center input-flex-wrap"
        style="margin-top: 10px;">
        <div class="d-flex  flex-column">
          <div class="p-1  lable-font" style="width: 85px;">Stock</div>
        </div>
        <div *ngFor="let code of stockCheckSum; let j = index" class="d-flex flex-column  ml-2  mtb-2">
          <input *ngIf="code.qty" type="text" style="width:60px;" value="{{stockCheckSum[j].qty}}" class="form-control"
            [readonly]='true'>
        </div>
      </div>
      <div class="form-group grade-class mt-4">
        <textarea class="form-control" id="usr" maxlength="30" [(ngModel)]="comment" (input)="updateData(comment,false)"
          name="comment" placeholder="Comment here"></textarea>
      </div>
      <!-- </form> -->
    </div>
    <div style="display: flex;flex-direction: column;">
      <div class="row">
        <div class="col-md-3">
          <strong>Total Units: </strong>
        </div>
        <div class="col-md-2">
          <strong>{{totalUnits}} </strong>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3">
          <strong>Total Quantity: </strong>
        </div>
        <div class="col-md-2">
          <strong>{{totalQuantity}} </strong>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3">
          <strong>Total MRP: </strong>
        </div>
        <div class="col-md-2">
          
          <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
            <strong>{{totalMRP || 0 | currency:'THB':'symbol-narrow'}}</strong>
            </ng-container>
            <ng-template #otherTenantsCurrency>
              <strong>{{ totalMRP  || 0 | currency:tenantConfig.currencySymbol ?tenantConfig.currencySymbol:'INR' }}</strong>
            </ng-template>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3">
          <strong>Total NET:</strong>
        </div>
        <div class="col-md-2">
          <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency1">
            <strong>{{netPrice  || 0 | currency:'THB':'symbol-narrow'}}</strong>
            </ng-container>
            <ng-template #otherTenantsCurrency1>
              <strong>{{ netPrice  || 0 | currency:tenantConfig.currencySymbol ?tenantConfig.currencySymbol:'INR' }}</strong>
            </ng-template>
        </div>
      </div>
    </div>
  </div>
  <div mat-dialog-actions class="btncontainer" *ngIf="isDialog">
    <div (click)="close()" class="placeorderbtncnl">Cancel</div>
    <div [mat-dialog-close]="" (click)="update($event)" class="placeorderbtn">Update</div>
  </div>
</div>


<!-- <div class="container">
    <h1 mat-dialog-title>Description</h1>
    <form>
      <div class="form-group">
        <label for="usr">Rating:</label>
        <input type="text" autocomplete="off" class="form-control" id="usr" (blur)="updateData()"
        [(ngModel)]="rating" name="rating" placeholder="Enter rating">
      </div>
      <div class="form-group">
        <label for="size">Size Ratio:</label>
        <input type="text" autocomplete="off" class="form-control" name="sizeRatio" (keyup)="populateSizeRatio($event)"
        length="1" size="1" maxlength="1" [(ngModel)]="sizeRatio">
      </div>

      <div *ngFor="let code of qtyData; let j = index" class="form-group">
        <label for="size">Quantity:</label>
        <input type="text" autocomplete="off" (keypress)="resetSizeRatioField()" appAutoTab="input{{j+1}}"
        class="form-control" (blur)="updateData()" maxlength="1" size="1"
        name="size{{sizeCodes[j]}}" [(ngModel)]="code.qty" id="{{sizeCodes[j]}}">
      </div>

      <div class="form-group">
        <label for="comment">Comment:</label>
        <textarea class="form-control" rows="3" cols="10" id="usr" [(ngModel)]="comment" (blur)="updateData()" name="comment"
        placeholder="Comment here"></textarea>
      </div>
      <div mat-dialog-actions class="btncontainer">
        <div (click)="onNoClick()" class="placeorderbtncnl">No Thanks</div>
        <div [mat-dialog-close]="" class="placeorderbtn">SUBMIT</div>
      </div>
    </form>
  </div> -->
