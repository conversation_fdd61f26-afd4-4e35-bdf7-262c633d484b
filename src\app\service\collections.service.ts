import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { collection } from 'app/model/collection.model';
import { User } from 'app/model/user.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CollectionsService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  public getAllCollections(): Observable<collection[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<collection[]>( Appconfig.BASE_URL+'api/techpack/collection/all', {
        headers:headers
      });
}
}
