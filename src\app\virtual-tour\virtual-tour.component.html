<div class="container-fluid mt-4 ml-2">

  <h4 class="table-head">
    <span>Virtual Tour</span>
    <button class="btn btn-primary ml-2 btn-sm" type="button" (click)="toggleTable()"
      style="display: inline-block; float: right; width: 80px; margin-right: 16px !important;">
      <span *ngIf="showTable">+ Create</span>
      <span *ngIf="!showTable">Close</span>
    </button>
    <input placeholder="Search" class="form-control"
      style="display: inline-block; width: 200px; float: right; margin-top: -2px; height: 31px; border-radius: 25px;"
      type="text" />
  </h4>

  <div class="row w-100 h-100 mt-4" style="height: 900px !important;">
    <ngx-datatable *ngIf="showTable" class="material" [rows]="rows" [columnMode]="'force'" [headerHeight]="50"
      [footerHeight]="50" [rowHeight]="50" [scrollbarV]="true" [sorts]="[{prop: 'name', dir: 'desc'}]"
      style="height: 75vh; width: 100%;">

      <ngx-datatable-column name="Name">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{row.name}}
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column name="No of Rooms">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{row.room}}
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column name="Status">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{row.status}}
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column name="Published Date">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{row.pdate}}
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column name="Actions">
        <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>
          <div class="row" style="width:100%">
            <div class="d-flex justify-content-center btn btn-sm  text-primary"><i title="Edit"
                class="fa fa-pencil"></i>
            </div>
            <div class="d-flex justify-content-center btn btn-sm  text-danger"><i title="Delete"
                class="fa fa-trash"></i>
            </div>
            <div class="d-flex justify-content-center btn btn-sm  text-info"><i title="View" class="fa fa-eye"></i>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

    </ngx-datatable>
    <iframe *ngIf="!showTable" class="w-100 h-100" src="http://localhost:8080/tool/" frameborder="0"></iframe>
  </div>
</div>