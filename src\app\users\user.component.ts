import { Component, OnInit ,ViewChild,TemplateRef} from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { UsersService } from '../service/users.services';
import { HttpErrorResponse } from '@angular/common/http';
import {Observable} from 'rxjs';
import {map, startWith} from 'rxjs/operators';
import { NgxFileDropEntry, FileSystemFileEntry, FileSystemDirectoryEntry } from 'ngx-file-drop';
import { Status } from 'app/model/status.model';
import {User} from '../model/user.model';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';


@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.scss']
})
export class UserComponent implements OnInit {
  rows: User[] = [];
  bacRows:User[] = [];
  User: User = new User();
  userForm: FormGroup;

  modalRef: BsModalRef;
  config = {
    animated: true,
    keyboard: true,
    backdrop: true,
    ignoreBackdropClick: false
  };
  files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  agentsList:any[];

  @ViewChild('popup') userModal ;
  @ViewChild('importUsersPopup') importUsersPopup ;
  myControl = new FormControl();
  options: string[] = ['One', 'Two', 'Three'];
  options1: string[] = ['One', 'Two', 'Three'];
  filteredOptions: Observable<string[]>;

  selectedUser: User = new User

  constructor(private userService: UsersService, private modalService: BsModalService, private toastr: ToastrService,public _formBuilder: FormBuilder) {
   
  }

  ngOnInit() {
    this.getAllUsers();
  
  }
  
  add(content) {
    this.selectedUser = new User;
    this.modalRef = this.modalService.show(content, Object.assign({}, this.config, { class: 'modal-lg' }));
  }

  getAllUsers() {
    this.userService.getUsers().subscribe(data => {
      this.rows = data;
      this.bacRows = [...data];
    });
  }
  
  intilizeUserForm() {
    this.userForm = this._formBuilder.group({
      customer_code: ["", Validators.required],
      customer_name: ["", Validators.required],
      customer_address1: ["", Validators.required],
      customer_address2: ["", Validators.required],
      customer_mobileNumber: ["", Validators.required],
      customer_mailId: ["", Validators.required],
      customer_city: ["", Validators.required],
      customer_pinCode: ["", Validators.required],
      customer_agentCode: [null, Validators.required],
      customer_parent: [null, Validators.required],
      searchCustomer: [""]
    });
  }

  addUser(customerData){
    let customerProfile={
        "customer":{
        "id":"",
        "code":customerData.code,
        "name":customerData.name,
        "cityName":customerData.cityName,
        "mobileNumber":customerData.mobileNumber,
        "mailId":customerData.mailId,
        "address1":customerData.address1,
        "address2":customerData.address2,
        "pinCode":customerData.pinCode,
        "stateCode":"",
        "format":"",
        "agentCode":'',
        "agentName":customerData.agentName,
        "validFBrands":"",
        "parent":customerData.parent
        },
        "userId":customerData.code,
        "password":customerData.code,
        "userName":customerData.name
        
  }
}
// editCustomer(data:any,status:string){
//   console.log(data);
//   if(status='edit'){
//     this.openModal(this.customerModal);
//     this.Customer.code = data.code;
//     this.Customer.name = data.name;
//     this.Customer.address1 = data.address1;
//     this.Customer.address2 = data.address2;
//     this.Customer.mobileNumber = data.mobileNumber;
//     this.Customer.mailId = data.mailId;
//     this.Customer.cityName = data.cityName;
//     this.Customer.pinCode = data.pinCode;
//   }

// }

updateFilter(event) {
  const val = event.target.value.toLowerCase();
  if (val == '') {
    this.rows = this.bacRows;
    return;
  }
  let temp = this.bacRows.filter(function (d) {
    return (d.userName == null ? "" : d.userName).toLowerCase().indexOf(val) !== -1 || (d.firstName == null ? "" : d.firstName).toLowerCase().indexOf(val) !== -1 || !val;
  });

  // update the rows
  this.rows = temp;
}

  edit(data,popupStatus){
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef =  this.modalService.show(template, Object.assign({}, this.config, { class: 'modal-lg' }));
  }

  setFiles(files: NgxFileDropEntry[]){
    this.files = files;
  }
  
  deleteFile(i){
    this.files.splice(i,1)
  }

  import(content) {
    this.modalRef = this.modalService.show(content, Object.assign({}, this.config, { class: 'modal-lg' }));
  }

  uploadUsers(){

  }

  downloadUsers(){

  }



}