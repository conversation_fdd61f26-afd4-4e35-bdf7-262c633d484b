<div [hidden]="myfilter" class="container-fluid p-1">
	<div class="d-flex flex-column">
		<!-- Filter Button -->
		<div class="d-flex flex-row-reverse ">
			<button type="button" class="btn btn-primary btn-primary-spacing" (click)="closeFilter()">Close</button>
			<button type="button" class="btn btn-primary btn-primary-spacing " (click)="clearFilters()">Clear</button>
			<button type="button" class="btn btn-primary" (click)="applyFilter()">Apply</button>

		</div>
		<div class="menu-wrapper">
			<!-- Filter -->
			<div class="child-menu-column" *ngFor="let eachObject of filterData | keyvalue"
				[hidden]="isObjectIsEmpty(eachObject.value.validValues,eachObject.value)">
				<div class="d-flex flex-column">
					<div>
						<strong>{{eachObject.value.name}}

							<!-- <mat-slider class="example-margin" [(ngModel)]="value"  [max]="max"  [step]="step" [thumbLabel]="thumbLabel"
							[min]="min" [vertical]="true">
							</mat-slider> -->
							<!-- <a *ngIf="selectedFilterValues.length > 0" class="btn-xs"
								(click)="clearFilters(eachObject.value.attribute)"><i class="fa fa-times-circle"
									style="color:coral"></i></a> -->
						</strong>
					</div>
					<div *ngIf="eachObject.value.name != 'MRP' ">
						<div class="checkbox filter-term">
							<label class="d-flex flex-row" *ngFor="let item of eachObject.value.validValues | keyvalue">
								<input type="checkbox" class=" mr-1 checkbox-input"
									[checked]="isChecked(eachObject.value,item.value)"
									(click)="handleFilterValues(eachObject.value,item.value)"> {{item.value}}
							</label>
						</div>
					</div>
					<div *ngIf="eachObject.value.name == 'MRP'">
						<div>
							<div>
								<mat-form-field appearance="outline" style="width: 100px;">
								  <mat-label>Min Value</mat-label>
								  <input matInput type="number" [(ngModel)]="min" (change)="onMinChange($event)" [min]="1" [max]="max" />
								</mat-form-field>
							  
								<mat-slider
								  thumbLabel
								  [displayWith]="formatLabel"
								  [min]="1"
								  [max]="max"
								  [color]="sliderColor"
								  [(ngModel)]="min"
								  (input)="onMinChange($event)"
								>
								</mat-slider>
							  </div>
							  <!-- <p>Min Value: {{ min }}</p> -->
							  
							  <div>
								<mat-form-field appearance="outline" style="width: 100px;">
								  <mat-label>Max Value</mat-label>
								  <input matInput type="number" [(ngModel)]="max" (change)="onMaxChange($event)" [min]="min" [max]="10000" />
								</mat-form-field>
							  
								<mat-slider
								  thumbLabel
								  [displayWith]="formatLabel"
								  [min]="min"
								  [max]="10000"
								  [color]="sliderColor"
								  [(ngModel)]="max"
								  (input)="onMaxChange($event)"
								>
								</mat-slider>
							  </div>
							  <!-- <p>Max Value: {{ max }}</p> -->
							  
						</div>


					</div>

				</div>
			</div>


		</div>
	</div>