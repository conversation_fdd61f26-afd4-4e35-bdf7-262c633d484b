import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Catalog } from 'app/model/catalog.model';
import { CatalogFilter } from 'app/model/CatalogFilter.model';
import { CustomerProfile } from 'app/model/customerprofile.model';
import { Order } from 'app/model/order.model';
import { OrderDetails } from 'app/model/OrderDetails.model';
import { OrderItem } from 'app/model/orderitem.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { User } from 'app/model/user.model';
import { CatalogService } from 'app/service/catalog.service';
import { OrderService } from 'app/service/order.service';
import { SizeratioService } from 'app/service/sizeratio.service';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Subscription } from 'rxjs';
import { Location } from '@angular/common'

@Component({
  selector: 'app-product-details',
  templateUrl: './product-details.component.html',
  styleUrls: ['./product-details.component.scss']
})
export class ProductDetailsComponent implements OnInit, AfterViewInit {

  productId: string;
  private subscription: Subscription = new Subscription();

  isSeeMore: boolean = false;
  isShowAmt: boolean = true;
  product: Catalog = new Catalog();
  tenantConfig: TenantConfig;
  images: any[] = [];
  orderItems: OrderItem[] = []
  customers: CustomerProfile[] = [];
  user: User;
  productColors: any[] = []
  catalogFilter: CatalogFilter;
  productItems: any[] = [];
  itemPosition: any;
  sizeQty: number;
  sizeRtioQty: any;
  sendProductDetails: BehaviorSubject<any> = new BehaviorSubject(null);
  stockData = [];
  stockCheckSum = [];
  purchasedOrderData: OrderItem[];
  isLoading: boolean = false;
  constructor(private route: ActivatedRoute, private _SizeratioService: SizeratioService, private router: Router, private orderService: OrderService,
    private toastrService: ToastrService,
    private catalogService: CatalogService,private location: Location) {

  }

  public ngOnInit(): void {
    this.intilizeProductDetails(true);
    this.getAlreadyPurchasedStyleCodes(this.productId);
  }

  intilizeProductDetails(isGetItemsRequiredToCall: boolean) {
    this.user = JSON.parse(localStorage.getItem("userProfile"));
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"))
    this.customers = JSON.parse(localStorage.getItem("customers"));
    if (this.tenantConfig.disableAmt != null && this.tenantConfig.disableAmt != undefined && this.tenantConfig.disableAmt == 1) {
      this.isShowAmt = false
    }
    document.getElementById('product-details-wrapper-id').scrollTop;
    this.subscription.add(this.route.params.subscribe(params => {
      this.productId = params['id'];
      this.getProductDetails(this.productId);
      this.catalogFilter = JSON.parse(sessionStorage.getItem('filter'));
      if (isGetItemsRequiredToCall) {
        this.getItems();
      } else {
      }
    }));
  }

  public ngAfterViewInit(): void {
    document.getElementById('product-details-wrapper-id').scrollTop;

  }


  getProductDetails(uid: string) {
    this.isLoading = true;
    this.catalogService.getByUid(uid)
      .subscribe(data => {
        this.orderService.getWishListItems().subscribe(res => {
          this.wishlist = res;
          if (this.wishlist.map(e => e.uuid).indexOf(data.uuid) != -1) {
            data.inWishlist = true
          } else {
            data.inWishlist = false;
          }
          this.productColors = [];
          this.product = data;
          this.sendProductDetails.next(this.product);
          var list = this.getThumbnailUrls(data);
          this.images = [];
          for (var i of list) {
            this.images.push({ 'url': this.getImgUrl(this.product, i) });
          }
          this.isLoading = false;
          let productColors: any = [];
          productColors = this.product.siblings;
          if (productColors != undefined && productColors.length > 2) {
            productColors = JSON.parse(productColors);
            productColors.map(item => {
              if (item.img) {
                this.productColors.push({ 'product': item, 'url': this.getImgUrl(this.product, item.img) });
              } else {
                this.productColors.push({ 'product': item, 'cc': item.cc });
              }
            });
          }
        });
      });

  }

  wishlist: any[] = [];
  getWIshlistItems(): void {
    this.orderService.getWishListItems().subscribe(res => {
      this.wishlist = res;
    });
  }

  getThumbnailUrls(item: Catalog): string[] {
    if (item.thumbnailImageUid != null && item.thumbnailImageUid.length > 0) {
      return item.thumbnailImageUid.split(",")
    }
    if (item.imageUid != null && item.imageUid.length > 0) {
      return item.imageUid.split(",")
    }
    else if(item.imageUid == null || item.imageUid == undefined)
    {
       const storeData = [item.uuid + ".jpg"];
      return storeData;
    }
    return [];
  }

  getImgUrl(item: Catalog, url: string) {
    if (this.tenantConfig.imageBaseUrl == undefined || this.tenantConfig.imageBaseUrl == null)
      return url
    else if (this.tenantConfig.imageBaseUrl.slice(-1) == '/') {
      return this.tenantConfig.imageBaseUrl + url;
    } else {
      return this.tenantConfig.imageBaseUrl + '/' + url;
    }
  }
  status:string = '';
  public getSizeQuantity(data: { items: any[], stockchecksum: any[] ,status:string}): void {
    this.orderItems = data.items;
    this.stockCheckSum = data.stockchecksum;
    this.status = data.status;
    // let sum = 0;
    // this.orderItems.forEach(item => {
    //   sum = sum + item.qty;
    // });
    // if (sum > parseInt(this.product.stockCheck)) {
    //   this.toastrService.warning('quantity exceeds stock! Please enter correct quantity');
    // }
  }

  public ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  public seeMore(): void {
    this.isSeeMore = true;
  }

  addToCart() {
    var det = new OrderDetails();
    var ord = new Order();
    var cust = this.customers[0]
    ord.customerUid = this.user.customerUid;
    ord.parentCustomerUid = cust.parentCustomerUid;
    det.order = ord;
    det.items = this.orderItems;

    if (this.orderItems?.length == 0 && this.status?.length == 0) {
      this.toastrService.error('Please Enter quantity or rating');
      return
    }
    let exceedCheck = 0;
    this.orderItems.forEach(item => {
      let result = this.stockCheckSum.find(obj => {
        return obj.sizecode === item.sizeCode
      })

      if ( result != undefined && item.qty > result.qty) {
        exceedCheck = exceedCheck + 1;
        console.log("exceedCheck",exceedCheck)
      }
    });
    if (exceedCheck > 0) {
      this.toastrService.warning('quantity exceeds stock! Please make sure quantity does not exceed stock');
    } else {
      this.orderService.saveOrUpdateOrder(det).subscribe(data => {
        this.toastrService.success('', 'Added to cart');
        this.getProductDetails(this.productId);
      });
    }

  }

  public addToWatchlist(): void {
    let wishList = {};
    wishList['id'] = this.product.id;
    wishList['name'] = this.product.name
    wishList['uuid'] = this.product.uuid
    wishList['catalogUid'] = this.product.uuid
    wishList['description'] = this.product.description
    wishList['shortCode'] = this.product.shortCode

    wishList['userUid'] = this.user.customerUid;
    wishList['offerMonth'] = this.product.offerMonth;
    wishList['offerMonthUid'] = this.product.offerMonthUid;
    wishList['catUid'] = this.product.catUid;
    wishList['subCatUid'] = this.product.subCatUid;

    wishList['addedOn'] = +new Date();
    wishList['price'] = this.product.price;
    wishList['title'] = this.product.title;
    wishList['status'] = this.product.status;
    wishList['createdDate'] = this.product.createdOn;
    wishList['tenantUid'] = this.product.tenantUid;
    wishList['opMode'] = null;
    let addWatchlist: any[] = [];
    addWatchlist.push(wishList);
    this.orderService.saveWishListItem(addWatchlist).subscribe(res => {
      this.toastrService.success('', 'Added to wishlist');
      this.product.inWishlist = true;
      this.getWIshlistItems();
    });
  }

  removeFromWishlist(): void {
    let wishList: any[] = [];
    let data = this.wishlist.filter(item => {
      return this.product.uuid == item.uuid;
    })[0];
    // let data = product;
    data['opMode'] = 2;
    wishList.push(data);
    this.orderService.saveWishListItem(wishList).subscribe(res => {
      this.toastrService.success('', 'Removed from wishlist');
      this.product.inWishlist = false;
      this.getWIshlistItems();
    });
  }

  getDefault(s: string): string {
    if (s == undefined || s == null) {
      return "NA";
    }
    return s;
  }

  likeProduct(): void {
    this.catalogService.likeProduct(this.product).subscribe(res => {
      this.toastrService.success('', 'Product Liked');
    })
  }
  public viewProductDetails(item): void {
    this.router.navigate(['/h/prodDet/' + item.cd]);
  }

  public getItems(): void {
    this.catalogService.getItems(this.catalogFilter).subscribe(data => {
      this.productItems = data;
      this.productItems.forEach((item, index) => {
        if (this.productId == item.uuid) {
          this.itemPosition = index;
        }
      });
    });
  }

  public previousItem(): void {
    if ((this.itemPosition > 0 && this.itemPosition <= this.productItems.length)) {
      this.itemPosition--;
      let item = this.productItems[this.itemPosition];
      this.productId = item.uuid;
      this.router.navigate(['/h/prodDet/' + this.productId]);
      this.intilizeProductDetails(false);
      // this.getProductDetails(this.productId);
    } else {
      this.toastrService.error('No items found');
    }
  }

  public nextItem(): void {
    if ((this.itemPosition >= 0 && this.itemPosition <= this.productItems.length)) {
      this.itemPosition++;
      let item = this.productItems[this.itemPosition];
      this.productId = item.uuid;
      this.router.navigate(['/h/prodDet/' + this.productId]);
      this.intilizeProductDetails(false);
      // this.getProductDetails(this.productId);
    } else {
      this.toastrService.error('No items found');
    }
  }

  public goBack(): void {
    this.location.back();
  }

  public getAlreadyPurchasedStyleCodes(productId: string): void {
    this.catalogService.getAlreadyPurchasedStyleCodes(productId).subscribe(res => {
      this.purchasedOrderData = [...res];
      console.log("already purchased response", res);
    })
  }

}
