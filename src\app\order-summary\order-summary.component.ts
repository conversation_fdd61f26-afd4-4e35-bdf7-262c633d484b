import { Component, OnInit, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { OrderService } from 'app/service/order.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { first } from 'rxjs/operators';

@Component({
  selector: 'app-order-summary',
  templateUrl: './order-summary.component.html',
  styleUrls: ['./order-summary.component.scss']
})
export class OrderSummaryComponent implements OnInit {
  public rows: any[];
  public orderSummary: any[] = [];
  public responseData: any[];
  public orderDetails: any[];
  public purchasedOrderDetail: any[];
  tenantConfig: TenantConfig;
  isOrderDetailSelected: boolean = false;
  public selectedTab: string = 'category'
  modalRef: BsModalRef;


  private orderUid: number;


  constructor(private _orderService: OrderService,
    private _activatedRoute: ActivatedRoute,
    private modalService: BsModalService,
    private router: Router,) { }

  ngOnInit(): void {
    this.getInitialData();
  }

  private getInitialData(): void {
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"))
    this._activatedRoute.params.pipe(first()).subscribe(params => {
      this.orderUid = params['orderUid'];
      this.getOrdersSummary(this.orderUid);
    })
  }

  private getOrdersSummary(orderUid: number): void {
    this._orderService.getOrdersSummary(orderUid).subscribe((orderSummary: any[]) => {
      this.responseData = orderSummary;
      console.log("responseData:",this.responseData)
      this.prepareDataForCategory(this.responseData);
    })
  }



  public populateData(selectedTab: string): void {
    if (selectedTab === 'category') {
      this.selectedTab = selectedTab;
      this.prepareDataForCategory(this.responseData);
    }
    if (selectedTab === 'subBrand') {
      this.selectedTab = selectedTab;
      this.prepareDataForSubCategory(this.responseData);
    }
    if (selectedTab === 'rating') {
      this.selectedTab = selectedTab;
      this.prepareDataForRating(this.responseData);
    }
  }

  private prepareDataForCategory(orderSummary: any[]): void {
    this.orderSummary = [];
    let unit: number = 0;
    let options: number = 0;
    let qty: number = 0;
    let price: number = 0;
    let netPrice: number = 0;
    for (let i = 0; i < orderSummary.length; i++) {
      if (orderSummary[i].catUid && orderSummary[i].subCatUid) {
        let index = this.orderSummary.findIndex(x => orderSummary[i].subCatUid == x.subCatUid);
        if (index > -1) {
          orderSummary[i].multiplier = orderSummary[i].multiplier?orderSummary[i].multiplier:1;
          let qty1 = orderSummary[i].qty > 0 ? orderSummary[i].qty * orderSummary[i].multiplier : 0;
          this.orderSummary[index].qty += qty1 || 0;
          this.orderSummary[index].unit += orderSummary[i].qty > 0 ? orderSummary[i].qty : 0;
          this.orderSummary[index].price += orderSummary[i].price > 0 ? orderSummary[i].price * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
          this.orderSummary[index].netPrice += orderSummary[i].netPrice > 0 ? orderSummary[i].netPrice * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
        } else {
          orderSummary[i].multiplier = orderSummary[i].multiplier?orderSummary[i].multiplier:1;
          let qty1 = orderSummary[i].qty > 0 ? orderSummary[i].qty * orderSummary[i].multiplier : 0;
          qty = qty1 || 0;
          unit = orderSummary[i].qty > 0 ? orderSummary[i].qty : 0;
          price = orderSummary[i].price > 0 ? orderSummary[i].price * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
          netPrice = orderSummary[i].netPrice > 0 ? orderSummary[i].netPrice * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
          this.orderSummary.push({ subCatUid: orderSummary[i].subCatUid, qty: qty, options: this.getOptionCounts(orderSummary[i].subCatUid, 'category'), unit: unit, price: price, netPrice: netPrice, styleCode: orderSummary[i].styleCode,rating:orderSummary[i].rating });
        }
      }

    }
  }

  private getOptionCounts(subCatUid: number, param: string): number {
    let categoryArray: any[] = [];
    let styleCodeArray: any[] = [];

    if (param == 'category') {
      categoryArray = this.responseData.filter(x => x.subCatUid == subCatUid);
    }

    if (param == 'subBrand') {
      categoryArray = this.responseData.filter(x => x.catUid == subCatUid);
    }

    if (param == 'rating') {
      categoryArray = this.responseData.filter(x => x.rating == subCatUid);
    }

    if (categoryArray.length > 0) {
      for (let i = 0; i < categoryArray.length; i++) {
        let index = styleCodeArray.findIndex(x => x.styleCode == categoryArray[i].styleCode)
        if (index < 0) {
          styleCodeArray.push(categoryArray[i]);
        }
      }

    }
    return styleCodeArray.length;
  }

  private prepareDataForSubCategory(orderSummary: any[]): void {
    this.orderSummary = [];
    let unit: number = 0;
    let options: number = 0;
    let qty: number = 0;
    let price: number = 0;
    let netPrice: number = 0;
    if (this.orderSummary.length == 0) {
      for (let i = 0; i < orderSummary.length; i++) {
        let index = this.orderSummary.findIndex(x => orderSummary[i].catUid == x.subCatUid);
        if (index > -1) {
          orderSummary[i].multiplier = orderSummary[i].multiplier?orderSummary[i].multiplier:1;
          this.orderSummary[index].qty += orderSummary[i].qty > 0 ? orderSummary[i].qty * orderSummary[i].multiplier : 0;
          this.orderSummary[index].unit += orderSummary[i].qty > 0 ? orderSummary[i].qty : 0;
          this.orderSummary[index].price += orderSummary[i].price > 0 ? orderSummary[i].price * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
          this.orderSummary[index].netPrice += orderSummary[i].netPrice > 0 ? orderSummary[i].netPrice * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
        } else {
          orderSummary[i].multiplier = orderSummary[i].multiplier?orderSummary[i].multiplier:1;
          qty = orderSummary[i].qty > 0 ? orderSummary[i].qty * orderSummary[i].multiplier : 0;
          unit = orderSummary[i].qty > 0 ? orderSummary[i].qty : 0;
          price = orderSummary[i].price > 0 ? orderSummary[i].price * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
          netPrice = orderSummary[i].netPrice > 0 ? orderSummary[i].netPrice * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;

          this.orderSummary.push({ subCatUid: orderSummary[i].catUid, qty: qty, options: this.getOptionCounts(orderSummary[i].catUid, 'subBrand'), unit: unit, price: price, netPrice: netPrice, styleCode: orderSummary[i].styleCode,rating:orderSummary[i].rating });
        }
      }
    }
  }

  private prepareDataForRating(orderSummary: any[]): void {
    this.orderSummary = [];
    let unit: number = 0;
    let options: number = 0;
    let qty: number = 0;
    let price: number = 0;
    let netPrice: number = 0;
    if (this.orderSummary.length == 0) {
      for (let i = 0; i < orderSummary.length; i++) {
        if (orderSummary[i].rating && orderSummary[i].rating != '') {
          let index = this.orderSummary.findIndex(x => orderSummary[i].rating == x.subCatUid);
          if (index > -1) {
            orderSummary[i].multiplier = orderSummary[i].multiplier?orderSummary[i].multiplier:1;
            this.orderSummary[index].qty += orderSummary[i].qty > 0 ? orderSummary[i].qty * orderSummary[i].multiplier : 0;
            this.orderSummary[index].unit += orderSummary[i].qty > 0 ? orderSummary[i].qty : 0;
            this.orderSummary[index].price += orderSummary[i].price > 0 ? orderSummary[i].price * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
            this.orderSummary[index].netPrice += orderSummary[i].netPrice > 0 ? orderSummary[i].netPrice * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
          } else {
            orderSummary[i].multiplier = orderSummary[i].multiplier?orderSummary[i].multiplier:1;
            qty = orderSummary[i].qty > 0 ? orderSummary[i].qty * orderSummary[i].multiplier : 0;
            unit = orderSummary[i].qty > 0 ? orderSummary[i].qty : 0;
            price = orderSummary[i].price > 0 ? orderSummary[i].price * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;
            netPrice = orderSummary[i].netPrice > 0 ? orderSummary[i].netPrice * (orderSummary[i].qty > 0 ? orderSummary[i].qty : 0) * orderSummary[i].multiplier : 0;

            this.orderSummary.push({ subCatUid: orderSummary[i].rating, qty: qty, options: this.getOptionCounts(orderSummary[i].rating, 'rating'), unit: unit, price: price, netPrice: netPrice, styleCode: orderSummary[i].styleCode, imageUid: orderSummary[i].imageUid,rating:orderSummary[i].rating });
          }
        }
      }
    }
  }

  public prepareDataForStyleCode(order:any): void {
    this.isOrderDetailSelected = true;
    this.orderDetails = []
    let unit: number = 0;
    let qty: number = 0;
    let price: number = 0;
    let netPrice: number = 0;
    let image: string
    let uid: string;
    let orderData:any[] = this.responseData.filter(item=>item.subCatUid == order.subCatUid)
    for (let i = 0; i < orderData.length; i++) {
      if (this.selectedTab == 'category') {
        uid = orderData[i].subCatUid;
      }

      if (this.selectedTab == 'subBrand') {
        uid = orderData[i].catUid;
      }
      let index = this.orderDetails.findIndex(x => orderData[i].styleCode == x.styleCode);
      if (index > -1) {
        orderData[i].multiplier = orderData[i].multiplier ? orderData[i].multiplier :1;
        this.orderDetails[index].qty += orderData[i].qty > 0 ? orderData[i].qty * orderData[i].multiplier : 0;
        this.orderDetails[index].unit += orderData[i].qty > 0 ? orderData[i].qty : 0;
        this.orderDetails[index].price += orderData[i].price > 0 ? orderData[i].price * (orderData[i].qty > 0 ? orderData[i].qty : 0) * orderData[i].multiplier : 0;
        this.orderDetails[index].netPrice += orderData[i].netPrice > 0 ? orderData[i].netPrice * (orderData[i].qty > 0 ? orderData[i].qty : 0) * orderData[i].multiplier : 0;
      } else {
        orderData[i].multiplier = orderData[i].multiplier ? orderData[i].multiplier :1;
        qty = orderData[i].qty > 0 ? orderData[i].qty * orderData[i].multiplier : 0;
        unit = orderData[i].qty > 0 ? orderData[i].qty : 0;
        price = orderData[i].price > 0 ? orderData[i].price * (orderData[i].qty > 0 ? orderData[i].qty : 0) * orderData[i].multiplier : 0;
        netPrice = orderData[i].netPrice > 0 ? orderData[i].netPrice * (orderData[i].qty > 0 ? orderData[i].qty : 0) * orderData[i].multiplier : 0;
        this.orderDetails.push({ subCatUid: uid, qty: qty, unit: unit, price: price, netPrice: netPrice, styleCode: orderData[i].styleCode, image: orderData[i].imageUid ? this.tenantConfig.imageBaseUrl + orderData[i].imageUid : this.tenantConfig.imageBaseUrl + orderData[i].catalogUid + ".jpg", rating: orderData[i].rating });
      }
    }
  }

  public prepareDataForStyleCodeRating(order: any): void {
    this.isOrderDetailSelected = true;
    this.orderDetails = [];
    order.image = this.tenantConfig.imageBaseUrl + order.imageUid
    this.orderDetails.push(order);
  }

  public calculateSumForQty(): number {
    let totalQty: number = 0;
    for (let obj of this.orderSummary) {
      totalQty += obj.qty;
    }
    return totalQty;
  }

  public calculateSumForUnit(): number {
    let totalUnit: number = 0;
    for (let obj of this.orderSummary) {
      totalUnit += obj.unit;
    }
    return totalUnit;
  }

  public calculateSumForOptions(): number {
    let totalOptions: number = 0;
    for (let obj of this.orderSummary) {
      totalOptions += obj.options;
    }
    return totalOptions;
  }

  public calculateSumForPrice(): number {
    let totalPrice: number = 0;
    for (let obj of this.orderSummary) {
      totalPrice += obj.price;
    }
    return totalPrice;
  }

  public calculateSumForNetPrice(): number {
    let totalNetPrice: number = 0;
    for (let obj of this.orderSummary) {
      totalNetPrice += obj.netPrice;
    }
    return totalNetPrice;
  }

  getImgUrl(url: string) {
    if (this.tenantConfig.imageBaseUrl == undefined || this.tenantConfig.imageBaseUrl == null)
      return url
    else if (this.tenantConfig.imageBaseUrl.slice(-1) == '/') {
      return this.tenantConfig.imageBaseUrl + url;
    } else {
      return this.tenantConfig.imageBaseUrl + '/' + url;
    }
  }

  getThumbnailUrls(item: any): string[] {
    if (item.thumbnailImageUid != null && item.thumbnailImageUid.length > 0) {
      return item.thumbnailImageUid.split(",")
    }
    if (item.imageUid != null && item.imageUid.length > 0) {
      return item.imageUid.split(",")
    }
    return []
  }

  public getPurchaseOrderDetailPopUp(order: any, template: TemplateRef<any>): void {
    this.purchasedOrderDetail = [];
    let uid: string;
    let config = {
      backdrop: true,
      ignoreBackdropClick: false
    }
    this.modalRef = this.modalService.show(
      template,
      Object.assign({ config }, { class: 'image-popup-wrapper modal-lg' })
    );
    if (this.selectedTab == 'category') {
      this.purchasedOrderDetail = this.responseData.filter(x => x.subCatUid == order.subCatUid).filter(y => y.styleCode == order.styleCode)
    }
    if (this.selectedTab == 'subBrand') {
      this.purchasedOrderDetail = this.responseData.filter(x => x.catUid == order.subCatUid).filter(y => y.styleCode == order.styleCode)
    }
    if (this.selectedTab == 'rating') {
      this.purchasedOrderDetail = this.responseData.filter(x => x.rating != '' && x.rating == order.subCatUid).filter(y => y.styleCode == order.styleCode)
    }
  }

  public getTotalQty(): number {
    let totalQty: number = 0;
    for (let i = 0; i < this.purchasedOrderDetail.length; i++) {
      if (this.purchasedOrderDetail[i].qty) {
        totalQty += this.purchasedOrderDetail[i].qty;
      }
    }
    return totalQty;
  }

  public goBack(routePath: string, subCatUid?: any): void {
    if (routePath == 'orderSummary') {
      this.router.navigate(['/h/orderGrid/']);
    }
    if (routePath == 'orderDetail') {
      this.isOrderDetailSelected = false;
    }
  }
}
