{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"chankya": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/assets/scss/style.scss", "node_modules/ngx-toastr/toastr.css", "src/assets/animate/animate.css", "node_modules/font-awesome/scss/font-awesome.scss", "node_modules/font-awesome/css/font-awesome.css", "node_modules/simple-line-icons/scss/simple-line-icons.scss", "node_modules/primeicons/primeicons.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeng/resources/themes/nova-light/theme.css", "node_modules/@ng-select/ng-select/themes/default.theme.css", "node_modules/swiper/css/swiper.css", "./node_modules/@swimlane/ngx-datatable/index.css", "./node_modules/@swimlane/ngx-datatable/themes/material.scss", "./node_modules/@swimlane/ngx-datatable/themes/dark.scss", "./node_modules/@swimlane/ngx-datatable/themes/bootstrap.scss", "./node_modules/@swimlane/ngx-datatable/assets/icons.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/popper.js/dist/umd/popper.js", "src/assets/js/bootstrap.js", "node_modules/magnific-popup/dist/jquery.magnific-popup.js"]}, "configurations": {"production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "chankya:build"}, "configurations": {"production": {"browserTarget": "chankya:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "chankya:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/popper.js/dist/umd/popper.js", "src/assets/js/bootstrap.js", "node_modules/slick-carousel/slick/slick.min.js"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/assets/scss/style.scss", "node_modules/ngx-toastr/toastr.css", "node_modules/font-awesome/scss/font-awesome.scss", "node_modules/simple-line-icons/scss/simple-line-icons.scss", "node_modules/slick-carousel/slick/slick.scss", "node_modules/slick-carousel/slick/slick-theme.scss"], "assets": ["src/assets", "src/favicon.ico"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": []}}}}, "chankya-e2e": {"root": "", "sourceRoot": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "chankya:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": []}}}}}, "defaultProject": "chankya", "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "scss"}, "@schematics/angular:directive": {"prefix": "app"}}, "cli": {"analytics": "816b8aff-9620-4f91-a38b-102a44e315a3"}}