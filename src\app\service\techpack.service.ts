import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { Bom } from 'app/model/bom.model';
import { image } from 'app/model/image.model';
import { LibraryItem } from 'app/model/LibraryItem.model';
import { Option } from 'app/model/option.model';
import { Status } from 'app/model/status.model';
import { techpack } from 'app/model/techpack.model';
import { User } from 'app/model/user.model';
import { IProductionFlow } from 'app/techpack/contracts/productionflow.interface';
import { FileSaverService } from 'ngx-filesaver';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TechpackService {

  http: HttpClient;
  basicAuth: string;
  selectedGroup: BehaviorSubject<string> = new BehaviorSubject('TECKPACKSTAGE');
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals, private fileSaverService: FileSaverService) {
    this.http = httpin;
    // this.basicAuth = this.globals.basicAuth;
    this.basicAuth = localStorage.getItem("basicAuth");
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }

  public setSelectedGroup(stage) {
    this.selectedGroup.next(stage);
  }

  getAllTechpacks(): Observable<techpack[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<techpack[]>(Appconfig.BASE_URL + 'api/techpack/all', {
      headers: headers
    });
  }

  downloadTechpack(uuid: string) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/pdf' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    this.http.get(Appconfig.BASE_URL + 'api/techpack/' + uuid + '/download/pdf', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, uuid + '.zip');
    });
  }

  updateTechpack(techpack: techpack): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid, techpack, {
      headers: headers
    });
  }

  getBom(uuid): Observable<Bom[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Bom[]>(Appconfig.BASE_URL + 'api/techpack/' + uuid + '/bom', {
      headers: headers
    });
  }

  getOptions(uuid): Observable<Option[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Option[]>(Appconfig.BASE_URL + 'api/techpack/' + uuid + '/option', {
      headers: headers
    });
  }


  getImages(uuid): Observable<image[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<image[]>(Appconfig.BASE_URL + 'api/techpack/' + uuid + '/sketch', {
      headers: headers
    });
  }



  getLibrary(): Observable<LibraryItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<LibraryItem[]>(Appconfig.BASE_URL + 'api/techpack/library', {
      headers: headers
    });
  }


  updateLibraryItem(item: LibraryItem): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/library', item, {
      headers: headers
    });
  }

  deactivateLibraryItem(itemUid: string): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Response>(Appconfig.BASE_URL + 'api/techpack/library/' + itemUid, {
      headers: headers
    });
  }

  activateLibraryItem(item: LibraryItem): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Response>(Appconfig.BASE_URL + 'api/techpack/trim/' + item.uuid + '/activate', item, {
      headers: headers
    });
  }

  downloadBoM(techpack: techpack) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/pdf' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    this.http.get(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/bom/csv/download', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, techpack.uuid + '-boms.csv');
    });
  }

  importBoM(techpack: techpack) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/pdf' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    this.http.get(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/bom/csv/import', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, techpack.uuid + '-boms.zip');
    });
  }

  generateQRTechpack(techpack: techpack): any {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    this.http.get(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/qrcode', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      return (<any>res).body;
    });
  }


  finalizeTechpack(techpack: techpack): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/finalize', {
      headers: headers
    });
  }

  reopenTechpack(techpack: techpack): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/reopen', {
      headers: headers
    });
  }

  cloneTechpack(techpack: techpack): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/clone', {
      headers: headers
    });
  }

  getAuditTechpack(techpack: techpack): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<any>(Appconfig.BASE_URL + 'api/techpack/' + techpack.uuid + '/audit', {
      headers: headers
    });
  }

  public sendToSourcing(techpack): Observable<IProductionFlow[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.post<IProductionFlow[]>(Appconfig.BASE_URL + 'api/productionflow/', techpack, {
      headers: headers
    });
  }

  public getProductionFlowByTechPack(techpackUid): Observable<IProductionFlow> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.get<IProductionFlow>(Appconfig.BASE_URL + 'api/productionflow/' + techpackUid + '/techpack', {
      headers: headers
    });

  }

  public updateSamplingDetails(samplingDetails): Observable<IProductionFlow> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.put<IProductionFlow>(Appconfig.BASE_URL + 'api/productionflow/sampling/', samplingDetails, {
      headers: headers
    })
  }

  public approveOrRejectSamplingDetails(techpack, status): Observable<IProductionFlow> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.put<IProductionFlow>(Appconfig.BASE_URL + 'api/productionflow/sampling/approveOrReject/' + status, techpack, {
      headers: headers
    });
  }

  public updateGoldSealDetails(goldsealDetails): Observable<IProductionFlow> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.put<IProductionFlow>(Appconfig.BASE_URL + 'api/productionflow/goldSeal/', goldsealDetails, {
      headers: headers
    })
  }

  public approveGoldSealDetails(status, techpack): Observable<IProductionFlow> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.put<IProductionFlow>(Appconfig.BASE_URL + 'api/productionflow/goldSeal/approveOrReject/' + status, techpack, {
      headers: headers
    });
  }

  public uploadBulkOrderFiles(productionFlowUId, fileToUpload) {

    let headers = new HttpHeaders({ "Accept": "application/json" })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);


    return this.http.post<IProductionFlow>(Appconfig.BASE_URL + 'api/productionflow/bulkOrder/' + productionFlowUId, fileToUpload, {
      headers: headers
    });

  }

}
