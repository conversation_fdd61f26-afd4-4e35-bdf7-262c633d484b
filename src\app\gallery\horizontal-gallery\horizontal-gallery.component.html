<div class="container-fluid">

    <div class="d-flex flex-wrap align-items-start justify-content-between search-results" infinite-scroll
        [infiniteScrollDistance]="ngxInfiniteScrollConfig.scrollDistance"
        [infiniteScrollUpDistance]="ngxInfiniteScrollConfig.scrollUpDistance"
        [infiniteScrollThrottle]="ngxInfiniteScrollConfig.throttle">

        <div class="p-3" *ngFor="let product of productItems | slice:0:6; let productIndex = index"
            style="width: 210px;">

            <div class="card">
                <div class="d-flex flex-column">
                    <ngb-carousel #carousel (click)="viewDetails(product)" [interval]="false" data-interval="false"
                        [pauseOnHover]="false" [showNavigationArrows]="false" [showNavigationIndicators]="false">
                        <ng-template ngbSlide *ngFor="let imageUrl of getThumbnailUrls(product);">
                            <a rel="nofollow noopener noreferrer">
                                <div class="picsum-img-wrapper">
                                    <img [defaultImage]="" [lazyLoad]="getImgUrl(imageUrl)" alt="{{product.title}}"
                                        style="display: block; width: 210px; height:280px;">
                                </div>
                            </a>
                        </ng-template>
                    </ngb-carousel>

                    <div class="d-flex flex-column p-3">
                        <div class="d-flex flex-row-reverse">
                            <i *ngIf="product.likeCount" class="fa fa-heart-o p-2" aria-hidden="true"
                                style="color: red;"><span style="padding: 2px;"
                                    class="text-dark">{{formatCount(product.likeCount)}}</span></i>
                            <i class="fa fa-eye text-primary p-2" *ngIf="product.viewCount" aria-hidden="true"><span
                                    style="padding: 2px;"
                                    class="text-dark">{{formatCount(product.viewCount)}}</span></i>
                        </div>
                        <strong class="product-title">{{product.uuid}}-{{product.title}}</strong>
                        <small class="product-description">{{product.brief}}</small>
                        <strong *ngIf="product.price && product.netPrice; else elseBlock">
                            MRP Price: <strong *ngIf="isShowAmt" class="product-price">
                                <div>
                                    <ng-container
                                        *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                                        {{product.price | currency:'THB':'symbol-narrow'}}
                                    </ng-container>
                                    <ng-template #otherTenantsCurrency>
                                        {{ product.price | currency:tenantConfig.currencySymbol ?
                                        tenantConfig.currencySymbol : 'INR' }}
                                    </ng-template>
                                </div>
                            </strong>
                            NET Price: <strong *ngIf="isShowAmt" class="product-price">

                                <div>
                                    <ng-container
                                        *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                                        {{product.netPrice | currency:'THB':'symbol-narrow'}}
                                    </ng-container>
                                    <ng-template #otherTenantsCurrency>
                                        {{ product.netPrice | currency:tenantConfig.currencySymbol ?
                                        tenantConfig.currencySymbol : 'INR' }}
                                    </ng-template>
                                </div>
                            </strong>
                        </strong>
                        <ng-template #elseBlock>
                            <strong *ngIf="product.price && !product.netPrice && isShowAmt" class="product-price">

                                <div>
                                    <ng-container
                                        *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                                        MRP Price: {{product.price  | currency:'THB':'symbol-narrow'}}
                                    </ng-container>
                                    <ng-template #otherTenantsCurrency>
                                        MRP Price: {{ product.price | currency:tenantConfig.currencySymbol ?
                                        tenantConfig.currencySymbol : 'INR' }}
                                    </ng-template>
                                </div>
                            </strong>
                            <strong *ngIf="!product.price && product.netPrice && isShowAmt" class="product-price">

                                <ng-container *ngIf="tenantConfig.currencySymbol == 'THB';else otherTenantsCurrency">
                                    NET Price: {{product.netPrice | currency:'THB':'symbol-narrow'}}
                                </ng-container>
                                <ng-template #otherTenantsCurrency>
                                    NET Price: {{ product.netPrice | currency:tenantConfig.currencySymbol ?
                                    tenantConfig.currencySymbol : 'INR' }}
                                </ng-template>
                            </strong>
                        </ng-template>

                    </div>

                </div>
            </div>

        </div>

    </div>

</div>