import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { BrandwiseSummary } from 'app/model/BrandwiseSummary.model';
import { Catalog } from 'app/model/catalog.model';
import { CustomerTarget } from 'app/model/CustomerTarget.model';
import { DashboardSummary } from 'app/model/DashboardSummary.model';
import { DaywiseSummary } from 'app/model/DaywiseSummary.model';
import { User } from 'app/model/user.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReportService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  getBrandSummary(): Observable<BrandwiseSummary[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({'Content-Type': 'application/json' })
     .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<BrandwiseSummary[]>( Appconfig.BASE_URL+'api/report/summary/*/*', {
        headers:headers
      });
}


  getProductSummary(brand: string): Observable<BrandwiseSummary[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<BrandwiseSummary[]>(Appconfig.BASE_URL + 'api/report/summary/'+brand+'/*', {
      headers: headers
    });
  }

  getDaywiseSummary(): Observable<DaywiseSummary[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<DaywiseSummary[]>(Appconfig.BASE_URL + 'api/report/daywise', {
      headers: headers
    });
  }


  getDashboardSummary(): Observable<DashboardSummary> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<DashboardSummary>(Appconfig.BASE_URL + 'api/report/dashboard', {
      headers: headers
    });
  }

  getTopList(cat: String,count: number,  ord: number): Observable<Catalog[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(Appconfig.BASE_URL + 'api/report/top10/'+cat+'/cat/'+count+'/count/'+ord+'/ord', {
      headers: headers
    });
  }

  getCustwiseSummary( ): Observable<CustomerTarget[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<CustomerTarget[]>(Appconfig.BASE_URL + 'api/report/custwise', {
      headers: headers
    });
  }

  getDistwiseSummary( ): Observable<CustomerTarget[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json'  })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<CustomerTarget[]>(Appconfig.BASE_URL + 'api/report/distwise', {
      headers: headers
    });
  }

}
