import 'rxjs/add/operator/map';

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Globals } from 'app/appconfig/appconfig.component';
import { OrderItem } from 'app/model/orderitem.model';
import { User } from 'app/model/user.model';
import { FileSaverService } from 'ngx-filesaver';
import { Observable, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { map } from 'rxjs/operators';
import * as pako from 'pako';
import { environment } from '../../environments/environment';
import { Appconfig } from '../appconfig/appconfig.component';
import { Catalog } from '../model/catalog.model';
import { CatalogFilter } from '../model/CatalogFilter.model';
import { CatalogFilterData } from '../model/CatalogFilterData.model';
import { Status } from '../model/status.model';
import { Order } from 'app/model/order.model'; import { HttpParams } from '@angular/common/http';


//import { saveAs } from 'file-saver/FileSaver';
@Injectable({
  providedIn: 'root'
})
export class CatalogService {

  http: HttpClient;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals, private fileSaverService: FileSaverService) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }

  createCatalog(cat: Catalog): Observable<Status> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Status>(`${environment.apiUrl}/api/catalog/`, cat, {
      headers: headers
    });
  }

  getDetails(uid: string): Observable<Catalog> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog>(`${environment.apiUrl}/api/catalog/` + uid, {
      headers: headers
    });
  }

  updateCatalog(cat: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/`, cat, {
      headers: headers
    });
  }

  updateCatalogStatus(cat: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/status`, cat, {
      headers: headers
    });
  }

  downloadCatalog(filter) {
    let headers = new HttpHeaders() //{'Content-Type': 'application/csv'}
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);
    this.http.post(`${environment.apiUrl}/` + 'api/catalog/filter/download', filter, {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      let time = new Date().getTime()
      this.fileSaverService.save((<any>res).body, time + '.csv');
    });
  }

  deleteOrderItem(catalogUid: string): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'ptuid': this.user.parentTenantUid })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Status>(`${environment.apiUrl}/api/catalog/orderItem/` + catalogUid, {
      headers: headers
    });
  }

  getItems(filter: CatalogFilter): Observable<Catalog[]> {
    return this.getUserProfile().pipe(
      switchMap(user => {
        let customerUid = user.customerUid;
        let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'ptuid': this.user.parentTenantUid })
          .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

        return this.http.post<Catalog[]>(`${environment.apiUrl}/api/catalog/filter/${customerUid}`, filter, {
          headers: headers
        }).pipe(map(item => {
          return item.map(obj => {
            obj.inWishlist = false;
            return obj;
          });
        }));
      })
    );
  }



  public getUserProfile(): Observable<any> {
    const userProfile = localStorage.getItem("userProfile");
    return of(JSON.parse(userProfile));
  }

  public getItemsOfZip(): Observable<any> {
    return this.getUserProfile().pipe(
      switchMap(user => {
        let customerUid = user.customerUid;
        let headers = new HttpHeaders({ 'Content-Type': 'application/zip', 'ptuid': user.parentTenantUid })
          .set("Authorization", localStorage.getItem("basicAuth")).set("ptuid", user.parentTenantUid);

        return this.http.get<any>(`${environment.apiUrl}/api/catalog/gzip/${customerUid}`, {
          headers: headers,
          responseType: 'blob' as 'json'
        });
      })
    );
  }


  getByUid(uid: string): Observable<Catalog> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'ptuid': this.user.parentTenantUid })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog>(`${environment.apiUrl}/api/catalog/${uid}`, {
      headers: headers
    });
  }

  getProductStock(stylecode): Observable<any> {
    let currentUser = JSON.parse(localStorage.getItem("currentUser"));
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'ptuid': this.user.parentTenantUid })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<any>(`${environment.apiUrl}/api/catalog/${stylecode.code}/stockMap/${currentUser.uuid}`, {
      headers: headers
    });
  }


  getSimilar(uid: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'ptuid': this.user.parentTenantUid })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/recomm/${uid}/similar`, {
      headers: headers
    });
  }


  getLiked(uid: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/recomm/${uid}/liked`, {
      headers: headers
    });
  }


  getBought(uid: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/recomm/${uid}/bought`, {
      headers: headers
    });
  }

  getProducts(vendorUid: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(vendorUid == null ? `${environment.apiUrl}/api/catalog/` :
      Appconfig.BASE_URL + 'api/catalog/' + vendorUid + '/', {
      headers: headers
    });
  }

  addProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.post<Status>(`${environment.apiUrl}/api/catalog/`, prod, {
      headers: headers
    });
  }

  updateProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/`, prod, {
      headers: headers
    });
  }

  approveProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/activate', null, {
      headers: headers
    });
  }

  deactivateProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/deactivate', prod, {
      headers: headers
    });
  }

  likeProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/like', null, {
      headers: headers
    });
  }

  unlikeProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/like', {
      headers: headers
    });
  }

  blockProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/block', null, {
      headers: headers
    });
  }

  notifymeProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/notifyme', null, {
      headers: headers
    });
  }

  unblockProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.delete<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/block', {
      headers: headers
    });
  }

  intentToBuyProduct(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/order', null, {
      headers: headers
    });
  }


  requestSample(prod: Catalog): Observable<Status> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.put<Status>(`${environment.apiUrl}/api/catalog/` + prod.uuid + '/sample', null, {
      headers: headers
    });
  }

  getItemsByType(type: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/catalog/` + type + '/type', {
      headers: headers
    });
  }

  getItemsByStatus(status: number): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/catalog/` + status + '/status', {
      headers: headers
    });
  }


  getInnovateItemsByType(type: string, index: number, count: number): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/catalog/inno/` + type + '/items', {
      headers: headers
    });
  }

  getItemsByVendor(vendorUid: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/catalog/` + vendorUid + '/vendor', {
      headers: headers
    });
  }


  suggest(term: string): Observable<Catalog[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Catalog[]>(`${environment.apiUrl}/api/search/suggest/` + term + '', {
      headers: headers
    });
  }


  getProductTypes(): Observable<string[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<string[]>(`${environment.apiUrl}/api/catalog/productTypes`, {
      headers: headers
    });
  }

  getAllFilterData(): Observable<CatalogFilterData> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<CatalogFilterData>(`${environment.apiUrl}/api/catalog/filterData`, {
      headers: headers
    });
  }

  getFilterData(type: string): Observable<string[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<string[]>(`${environment.apiUrl}/api/catalog/filterdata/` + type, {
      headers: headers
    });
  }

  getFilterDataByBucket(type: string, bucket: string): Observable<string[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<string[]>(`${environment.apiUrl}/api/catalog/filterdata/` + type + '/' + bucket, {
      headers: headers
    });
  }


  getFilterDataByBucketCategory(type: string, bucket: string, catType: string): Observable<string[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<string[]>(`${environment.apiUrl}/api/catalog/filterdata/` + type + '/' + bucket + '/' + catType, {
      headers: headers
    });
  }


  getFilterDataByBucketSubCategory(type: string, bucket: string, catType: string, subcattype: string): Observable<string[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<string[]>(`${environment.apiUrl}/api/catalog/filterdata/` + type + '/' + bucket + '/' + catType + '/' + subcattype, {
      headers: headers
    });
  }


  saveToFileSystem(response) {
    // const contentDispositionHeader: string = response.headers.get('Content-Disposition');
    //const parts: string[] = contentDispositionHeader.split(';');
    const filename = "catalog.csv";//parts[1].split('=')[1];
    const blob = new Blob([response], { type: 'text/plain' });
    //    saveAs(blob, filename);
  }
  getFilterByCategory(subCatUid) {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<any[]>(`${environment.apiUrl}/api/catalog/${subCatUid}/filter/`, {
      headers: headers
    });
  }

  getAlreadyPurchasedStyleCodes(catalogUid: string): Observable<OrderItem[]> {

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<OrderItem[]>(`${environment.apiUrl}/api/order/${catalogUid}/itemsList`, {
      headers: headers
    });
  }

  getShipment(customerUid: string): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth || '').set("ptuid", this.user.parentTenantUid);

    return this.http.get<any[]>(`${environment.apiUrl}/api/catalog/getShipment/${customerUid}`, {
      headers: headers
    });
  }

}
