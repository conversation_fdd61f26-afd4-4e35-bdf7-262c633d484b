import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'searchbyStyleCode'
})
export class SearchbyStyleCodePipe implements PipeTransform {

  transform(list: any[], filterText: string): any {
    if (list) {
      return list.filter(item => {
        if (item.code?.search(new RegExp(filterText, 'i')) > -1 || item.title?.search(new RegExp(filterText, 'i')) > -1 || item.description?.search(new RegExp(filterText, 'i')) > -1 || item.shortCode?.search(new RegExp(filterText, 'i')) > -1 || item.eanCode?.search(new RegExp(filterText, 'i')) > -1 || item.uuid?.search(new RegExp(filterText, 'i')) > -1) {
          return item;
        }
      })
    } else {
      return []
    }
  }

}
