
export class Catalog {

  name: string;
  uuid: string;
  catUid: string;
  subCatUid: string;
  offerMonthUid: string;
  offerMonth: string;
  features: string;
  tags: string;
  fabricCode: string;
  designGroup: string;
  shortCode: string;
  description: string;
  code: string;
  brief: string;
  title: string;
  vendorUid: string;
  vendorName: string;
  origin: string;
  price: number;
  fromPrice: number;
  toPrice: number;
  likeCount: number;
  productionLeadTime: number;
  productionDetails: string;
  compositionDetails: string;
  marketDetails: string;
  samplePolicy: string;
  sampleRequestedByUserUid: string;
  imageUid: string;
  thumbnailImageUid: string;
  productType: string;
  subCategoryType: string;
  categoryType: string;
  subbrand: string;
  imageUids: string[];
  sortBy: string;
  siblings?: string[];
  tenantUid?: string[];
  id: number;
  lastName: string;
  modifiedBy: number;
  modifiedOn: string;
  modifiedTime: number;
  status: number;
  active: boolean;
  isActive: number;
  isNew: string;
  createdBy: number;
  createdOn: string;
  createdTime: number;
  likeStatus: number;
  siblingColorCodes: string;
  siblingColorNames: string;
  siblingCatalogUid: string;
  groupUid: string;
  techPackUid: string;
  collectionUid: string;

  color: string;
  fitType: string;
  fit: string;
  gender: string;
  occasion: string;
  productStory: string;
  catalogPriceSizeCodes?:string;
  availableSizeCodes?:string;
  warehouseStockQty?:number;
  collectionStory: string;
  fabricCount: string;
  pattern: string;
  sleeve: string;
  garmentTreatment: string;
  closureType: string;
  exclusivity: string;
  stockCheck?:number;
  minOrderQty: number;
  maxOrderQty: number;
  maxProductionLeadTime: number;
  minProductionLeadTime: number;

  blockedByUserUid: string;
  boughtByUserUid: string;
  blockedOn: string;
  soldOn: string;
  compliance: string;
  imageUid3D: string;
  imageUid3DColCount: number;

  detailPdfUid: string;
  bucket: string;

  addedToCartByUserUid: string;
  highresImageUid: string;
  category: string;
  location: string;
  netPrice: number;
  styleCode: string;
  inWishlist: boolean;
  prices:any[]
  //shortCode: string;
}