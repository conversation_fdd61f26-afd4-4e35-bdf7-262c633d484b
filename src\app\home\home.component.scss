// div#masonry { 
//   display: -webkit-box; 
//   display: -webkit-flex; 
//   display: -ms-flexbox; 
//   display: flex; 
//   -webkit-box-orient: vertical; 
//   -webkit-box-direction: normal; 
//   -webkit-flex-direction: column; 
//       -ms-flex-direction: column; 
//           flex-direction: column; 
//   -webkit-flex-wrap: wrap; 
//       -ms-flex-wrap: wrap; 
//           flex-wrap: wrap;
//   height: 100vw;
// }
// div#masonry img {  
//   width: 33.4%;
//   -webkit-transition: .5s opacity;
//           transition: .5s opacity;
// } 

// div#masonry:hover img { opacity: 0.5; }
// div#masonry:hover img:hover { opacity: 1; } 

// /* fallback for earlier versions of Firefox */

// @supports not ((-webkit-flex-wrap: wrap) or (-ms-flex-wrap: wrap) or (flex-wrap: wrap)) {
//   div#masonry { display: block; }
//   div#masonry img {  
//   display: inline-block;
//   vertical-align: top;
//   }
// }



// .flex-container {
//   background-color: #f2f2f2;
//   padding: 10px;
//   border: 2px solid #CCCCCC;
//   max-width: 900px;
//   margin: 0 auto;
//   height: 470px;
//   display: flex;
//   flex-flow: column wrap; /* Shorthand – you could use ‘flex-direction: column’ and ‘flex-wrap: wrap’ instead */
//   justify-content: flex-start;
//   align-items: flex-start;
// }

// .flex-item {
//   background-color: orange;
//   height: 150px;
//   width: 31%;
//   margin: 1%;
//   padding: 10px;
// }

// .flex-item:nth-child(2) {
//   background-color: pink;
//   height: 250px;
// }

// .flex-item:nth-child(3) {
//   height: 190px;
// }

// .flex-item:nth-child(4) {
//   background-color: aqua;
//   height: 220px;
// }

// .demo-one {
//   height: 260px;
//   background-color: #FFFFFF;
// }

// .demo-one img {
//   height: 400px;
//   width: 260px;
// }

// .promotions-one {
//   height: 335px;
//   background-color: #FFFFFF;
// }

// .promotions-one img {
//   height: 335px;
//   width: 260px;
// }

// .dots-container[_ngcontent-jka-c161] > .dot[_ngcontent-jka-c161] {
//   background: #020202 !important;
//   border-radius: 5px;
//   height: 10px;
//   margin-left: 4px;
//   margin-right: 4px;
//   width: 10px;
//   cursor: pointer;
//   opacity: .5;
// }
// .second-row-images{
//   display: flex;
//   flex-direction: row;
//   height: 1000px;
//   transition: transform .3s;
// }
// .row{
//   display: flex;
//   flex-direction: row;
//   width: 100%;
// }
// .third-row-images{
//   display: flex;
//   flex-direction: column;
//   width: 100%;
//   padding:15px;
//   height: 600px;
// }
// ::ng-deep .ng-image-slider .ng-image-slider-container .main {
//   overflow: hidden;
//   position: absolute;
//   width: 100%;
//   height: 500px !important
// }
// ::ng-deep .img-div  {
//   width: 350px!important;
// }
// .fourth-row-images{
//   display: flex;
//   flex-direction: row;
//   width: 100%;
//   padding:15px;
// }
.carouselImage {
   width:100%;
   height: 90vh;

   }
::ng-deep .container-fluid { padding:0 !important; }

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  top: 48%;
}
.carousel-title{
  padding:10px;
  color: #ffffff;
  font-size: 5.5vw;
  line-height: 0.7;
  font-weight: bold;
}
.arrow-1,.arrow-2,.arrow-3{
  color:black;
  position: absolute;
  bottom: 3%;
  left: 50%;
}
.material-icons{
  font-size: 30px;
}
::ng-deep .carousel-indicators li{
  height: 10px !important;
  border: 1px solid black;
  border-radius: 5px;
}