import { Component, OnInit, ViewEncapsulation,TemplateRef } from '@angular/core';
import { Router } from "@angular/router";
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

import { AuthService } from 'app/service/auth/auth.service';
import { UserService } from 'app/service/user.service';
import { CustomersService } from 'app/service/customers.service';

import { CustomerProfile } from 'app/model/customerprofile.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { Globals } from '../../appconfig/appconfig.component';

import { environment } from '../../../environments/environment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';


@Component({
  selector: 'ms-loginone-session',
  templateUrl: './loginone-component.html',
  styleUrls: ['./loginone-component.scss'],
  encapsulation: ViewEncapsulation.None,
})

export class LoginoneComponent {

  email: string = "";
  modalRef: BsModalRef;
  password: string = "";
  mobForm: FormGroup;
  otpForm: FormGroup;
  name: string;
  isInvalidCreds: boolean = false;
  _showMobileNumForm: boolean = false;
  _showSignInForm: boolean = true;
  _showOtpForm: boolean = false;
  tenantLogo: string = '';
  tenantConfig: TenantConfig;
  credsErrorMessage: string = 'Invalid email/password.';

  constructor(private router: Router,
    private authService: AuthService,
    private userService: UserService,
    private customersService: CustomersService,
    private globals: Globals,
    private _formBuilder: FormBuilder,
    private modalService: BsModalService,
    public translate: TranslateService) { }

  ngOnInit(): void {
    this.tenantLogo = 'assets/img/' + environment.tenantUid + '_logo.jpg';
    this.mobForm = this._formBuilder.group({
      mobilenumber: ['', Validators.required]
    });
    this.otpForm = this._formBuilder.group({
      otpnumber: ['', Validators.required]
    });
    // this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
  }

  // when email and password is correct, user logged in.
  logIn(value) {
    // this.authService.loginUser(value);
    localStorage.removeItem("basicAuth");
    localStorage.removeItem("tenantConfig");
    localStorage.removeItem("token");
    localStorage.removeItem("userProfile");
    localStorage.removeItem("currentUser");
    localStorage.removeItem("itemData");
    if (value == 'roadshow') {
      this.globals.tenantUid = environment.tenantUid;
    } else {
      this.globals.tenantUid = environment.readyStockTenantUid;
    }
    this.authService.login(this.email, this.password, this.globals.tenantUid)
      .subscribe(
        user => {
          // this.globals.basicAuth = 'Basic ' + btoa(this.email + ':' + this.password);
          this.globals.setBasicAuth('Basic ' + btoa(this.email + ':' + this.password));
          this.globals.user = user;
          // localStorage.setItem("basicAuth", this.globals.basicAuth);
          localStorage.setItem("token", this.globals.basicAuth);
          localStorage.setItem("userProfile", JSON.stringify(user));
          this.getCustomerDetails();
        },
        error => {
          //this.error = error;
          //this.loading = false;
          if (this.email == '' && this.password == '') {
            this.credsErrorMessage = 'Please Enter credentials.';
          } else {
            this.credsErrorMessage = 'Invalid email/password.';
          }
          this.isInvalidCreds = true;
        });
  }


  // when email and password is correct, user logged in.
  genereateOTP({ value, valid }: { value, valid: boolean }) {
    console.log("value", value);
    this._showMobileNumForm = false;
    this._showSignInForm = false;
    let mobileNumber = {
      mobilenumber: value.mobilenumber,
    }
    this.authService.generateOtpNum(mobileNumber).subscribe(res => {
      console.log("res", res);
      this._showOtpForm = true;
    })


  }

  logInwithOtp({ value, valid }: { value, valid: boolean }) {
    let otpData = {
      otpnumber: value.otpnumber,
    }
    this.authService.verifyOtpNum(otpData).subscribe(res => {
      console.log("resotp", res);
      // this.router.navigate(['/h/home']);
      //this._showOtpForm = false;
    })

  }

  resetInvalid() {
    this.isInvalidCreds = false;
  }

  getMobileNumberScreen() {
    this._showMobileNumForm = true;
    this._showSignInForm = false;
  }
  showSignin() {
    this._showMobileNumForm = false;
    this._showSignInForm = true;
    this._showOtpForm = false

  }

  getTenantConfig(user) {
    this.userService.getTenantConfig(user).subscribe(data => {
      localStorage.setItem("tenantConfig", JSON.stringify(data));

      this.router.navigate(['/h/home']);
    });
  }

  getCustomerDetails() {
    this.customersService.getCustomers().subscribe(data => {
      localStorage.removeItem("customers")
      localStorage.setItem("customers", JSON.stringify(data));
      this.getTenantConfig(this.globals.user);
    });
  }
}



