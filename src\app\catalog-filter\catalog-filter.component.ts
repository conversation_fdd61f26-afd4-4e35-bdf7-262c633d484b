import { Component, OnInit, ElementRef, Renderer2, ViewChild, Output, EventEmitter, Input } from '@angular/core';

import { Subscription } from "rxjs";

import { ActivatedRoute, Router, NavigationStart, NavigationEnd } from '@angular/router';


import { ToastrService, ToastrConfig } from 'ngx-toastr';
import { AppconfigComponent, Appconfig, Globals, MessageService } from '../appconfig/appconfig.component';

import { User } from '../model/user.model';
import { MasterDataItem } from '../model/MasterDataItem.model';
import { Catalog } from '../model/catalog.model';
import { CategoryType } from 'app/model/CategoryType.model';
import { CatalogFilter } from 'app/model/CatalogFilter.model';
import { CatalogService } from '../service/catalog.service';
import { UserService } from '../service/user.service';
import { FilterService } from '../service/filter.service';

import { Order } from '../model/order.model';
import { OrderItem } from '../model/orderitem.model';

import { BsModalService, ModalModule } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';

import { DragScrollComponent } from "ngx-drag-scroll";
import { ThemePalette } from '@angular/material/core';

@Component({
  selector: 'catalog-filter',
  templateUrl: './catalog-filter.component.html',
  styleUrls: ['./catalog-filter.component.scss']
})
export class CatalogFilterComponent implements OnInit {


  @Output() onFilter = new EventEmitter<any>(null);
  @Output() closeCatalogFilter = new EventEmitter<any>();

  sliderColor: ThemePalette = 'primary';
  eventSubscription: Subscription;

  costSlider: any;

  private subscription2: Subscription;
  productItems: Catalog[] = [];
  imageUrl: string;
  sortBy: string;

  fabricTypes: MasterDataItem[] = [];
  womenGarments: string[] = [];
  colors: MasterDataItem[] = [];
  gender: MasterDataItem[] = [];
  offerMonths: MasterDataItem[] = [];
  fits: any[] = [];
  stories: any[] = [];
  complainces: any[] = [];
  prodStatusList: any[] = [];
  productTypes: CategoryType[] = [];
  isExpandFabricType: boolean = false;
  isExpandFit: boolean = false;
  isExpandGender: boolean = true;
  isExpandColor: boolean = false;
  isExpandVendor: boolean = false;
  isExpandStory: boolean = false;
  isExpandProductType: boolean = false;
  isExpandOfferMonth: boolean = false;
  myfilter: boolean = false;

  selectedLeadtime: number;
  selectedPrice: number;

  catalogMode: string = 'S';

  displayedStories: any[] = [];
  displayedOfferMonths: any[] = [];
  selectedStories: any[] = [];
  selectedColors: any[] = [];
  selectedVendors: string[] = [];
  selectedCategories: string[] = [];
  selectedTypes: string[] = [];
  selectedFabricTypes: string[] = [];
  selectedFits: string[] = [];
  selectedTrims: string[] = [];
  selectedGenders: string[] = [];
  selectedWomenGarments: string[] = [];
  selectedMenGarments: string[] = [];
  selectedCompliance: string[] = [];
  selectedProductStatusList: any[] = [];
  selectedProductTypes: string[] = [];
  selectedOfferMonths: string[] = [];

  fromPrice: number;
  // toPrice: number;

  isStorySelected: boolean = false;
  isShowGender: boolean = false;
  isShowStory: boolean = false;
  isShowCategory: boolean = false;
  isShowFabric: boolean = false;
  isShowVendor: boolean = false;
  isShowTrim: boolean = false;
  isShowFit: boolean = false;
  isShowWash: boolean = false;
  isShowProductStatus: boolean = false;
  isShowProductType: boolean = false;
  isShowCompliance: boolean = false;
  isShowTags: boolean = false;
  isShowCost: boolean = false;
  isShowLeadTime: boolean = false;
  isShowOfferMonth: boolean = false;

  isRefreshStories: boolean = true;

  isLoadingDone: boolean = false;

  isUnderConstruction: boolean = false;

  rangeValues: number[] = [20, 80];

  sortByName: string = '';
  searchTerm: string;
  bucket: string;
  vendorUid: string;
  productType: string;
  categoryType: string;
  subCategoryType: string;
  currentUser: User;

  lastRoute: string;
  lastPosition: number = 0;
  preparedFilter: CatalogFilter;
  subCatId: any;
  filterData: any;
  min: number = 1;      // Default minimum value
  max: number = 10000;  // Default maximum value
  //filterwise categories

  selectedFilterValues: Object = {};


  sortByCodes = [{ 'code': 'NEW', 'name': 'Whats New' }, { 'code': 'POP', 'name': 'Popularity' },
  { 'code': 'ASCPRI', 'name': 'Low to High' }, { 'code': 'DESCPRI', 'name': 'High to Low' }, { 'code': 'STATUS', 'name': 'Status' }];


  constructor(private router: Router, private route: ActivatedRoute, private modalService: BsModalService, private globals: Globals, private messageService: MessageService,
    private catalogService: CatalogService, private _FilterService: FilterService, private toastrService: ToastrService,) {

    this.prodStatusList = [{ 'code': 'O', 'name': 'Available' }, { 'code': 'B', 'name': 'Blocked' }, { 'code': 'S', 'name': 'Sold' }];
    this.eventSubscription = this.messageService.getMessage().subscribe(message => {

      if (message.message == 'CLEAR_FILTERS') {
        // this.resetFilter();
        // this.loadPage(this.route.snapshot.params);
      }

    });


  }

  ngOnInit() {

    this._FilterService.menuChanges.subscribe(res => {
      if (res) {
        this.selectedFilterValues = {};
        this._FilterService.saveBackupFilter(null)
        // this.clearFilters();
      }
    })
    this.route.params.subscribe(
      params => {
        this.subCatId = params.subcat
        let filter = localStorage.getItem("filter");
        if (localStorage.getItem("fromDetailsBackButton") != null && filter != null) {
          localStorage.removeItem("fromDetailsBackButton");
          let filterobj: CatalogFilter = JSON.parse(filter);
          this.selectedProductTypes = filterobj.productTypes;
          for (let s of this.selectedProductTypes) {
            this.selectedProductTypes[s] = true;
          }
          this.categoryType = filterobj.categoryType;
          this.isStorySelected = filterobj.isStorySelected;

          this.subCategoryType = filterobj.subCategoryType;
          // this.selectedCategories = filterobj.subCategoryTypes;
          this.selectedFits = filterobj.fit;
          for (let s of this.selectedFits) {
            this.selectedFits[s] = true;
          }
          this.selectedTypes = filterobj.subCategoryTypes;
          for (let s of this.selectedTypes) {
            this.selectedTypes[s] = true;
          }
          this.selectedLeadtime = filterobj.leadTime;
          this.fromPrice = filterobj.fromPrice;
          // this.toPrice = filterobj.toPrice;
          this.selectedStories = filterobj.stories;
          for (let s of this.selectedStories) {
            this.selectedStories[s] = true;
          }
          this.selectedProductStatusList = filterobj.statusCodes;
          for (let s of this.selectedTypes) {
            this.selectedProductStatusList[s] = true;
          }
          this.searchTerm = filterobj.searchTerm;
          this.selectedVendors = filterobj.vendorUids;
          for (let s of this.selectedVendors) {
            this.selectedVendors["'" + s + "'"] = true;
          }
          this.isRefreshStories = false;
          this.displayedStories = filterobj.displayedStories;
          this.stories = filterobj.displayedStories;
          this.bucket = filterobj.bucket;
          this.catalogMode = filterobj.catalogMode;
          this.sortBy = filterobj.sortBy;
          for (let s of this.sortByCodes) {
            if (s.code == this.sortBy) {
              this.sortByName = s.name;
            }
          }
          // this.filter();
          localStorage.removeItem("filter");

        } else {
          //this.loadPage(this.route.snapshot.params);
          this.getFilterData();
        }
      });


    // this.getFilterData();



  }
  disable: boolean;
  onMinChange(event: any) {
    // Adjust max if min exceeds max
    if (this.min >= this.max) {
      this.min = this.max - 1;
    }
  }

  onMaxChange(event: any) {
    // Adjust min if max is less than min
    if (this.max <= this.min) {
      this.max = this.min + 1;
    }
  }

  formatLabel(value: number): string {
    return value.toString();
  }

  getFilterData() {
    this.catalogService.getFilterByCategory(this.subCatId).subscribe(res => {
      this.filterData = res;
    });
  }

  public isObjectIsEmpty(data, filteredObject): boolean {
    return (filteredObject.name != 'MRP') ? (Object.keys(data).length === 0 && data.constructor === Object) : false;
  }

  public handleFilterValues(parentObject, item) {
    if (this.filterData.find(item => item.name == 'MRP')) {
      // this.selectedFilterValues['']
    }
    let key = parentObject.attribute;
    if (parentObject.multi == 0) {
      if (this.selectedFilterValues.hasOwnProperty(key)) {


        let data: any[] = this.selectedFilterValues[key];
        if (data.indexOf(item) > -1) {
          let i = this.selectedFilterValues[key].indexOf(item);
          this.selectedFilterValues[key].splice(i, 1);
        } else {
          this.selectedFilterValues[key] = [];
          this.selectedFilterValues[key].push(item);
        }

      } else {
        this.selectedFilterValues[key] = [];
        this.selectedFilterValues[key].push(item);
      }
    } else {
      console.log("this.selectedFilterValues[key]", key)
      this.selectedFilterValues['fromPrice'] = this.min;
      this.selectedFilterValues['toPrice'] = this.max;
      if (this.selectedFilterValues.hasOwnProperty(key)) {
        let data: any[] = this.selectedFilterValues[key];
        if (data.indexOf(item) > -1) {
          let i = this.selectedFilterValues[key].indexOf(item);
          this.selectedFilterValues[key].splice(i, 1);
          if (this.selectedFilterValues[key].length == 0) {
            delete this.selectedFilterValues[key];
          }
        } else {
          this.selectedFilterValues[key].push(item);
        }

      } else {
        this.selectedFilterValues[key] = [];
        this.selectedFilterValues[key].push(item);
      }

    }
    this._FilterService.saveBackupFilter(this.selectedFilterValues);
  }
  public isChecked(parentObject, item) {
    let key = parentObject.attribute;
    if (this.selectedFilterValues.hasOwnProperty(key)) {
      return this.selectedFilterValues[key].indexOf(item) > -1 ? true : false
    } else {
    }

  }

  public applyFilter(): void {
    if (this.min > 1 || this.max < 10000) {
      this.selectedFilterValues['fromPrice'] = this.min;
      this.selectedFilterValues['toPrice'] = this.max;
    }
    if (Object.keys(this.selectedFilterValues).length === 0) {
      this.toastrService.warning("Please select atleast one option")
    }
    else {
      console.log("selectedFilterValues", this.selectedFilterValues)
      this.onFilter.emit(this.selectedFilterValues);
      this.closeCatalogFilter.emit(true);
    }
  }

  public clearFilters(): void {
    this.selectedFilterValues = {};
    this.min = 1;
    this.max = 10000;
    this._FilterService.saveBackupFilter(null)
    this.onFilter.emit('empty');
  }
  closeFilter() {
    this.closeCatalogFilter.emit(true);
    this.clearFilters();
  }




  // prepareFilter(): CatalogFilter {
  //   let newstat = [];
  //   for (let s of this.selectedProductStatusList) {
  //     if (s == 'O') {
  //       newstat.push(0);
  //     } else if (s == 'B') {
  //       newstat.push(1);
  //     } else if (s == 'S') {
  //       newstat.push(3);
  //     }
  //   }

  //   let fi = new CatalogFilter();
  //   fi.subCategoryTypes = this.selectedTypes;
  //   fi.fabric = this.selectedFabricTypes;
  //   fi.trim = this.selectedTrims;
  //   if (this.selectedLeadtime != 0)
  //     fi.leadTime = this.selectedLeadtime;
  //   fi.price = this.selectedPrice;
  //   fi.fromPrice = this.fromPrice;
  //   // fi.toPrice = this.toPrice;
  //   fi.sortBy = this.sortBy;
  //   fi.fit = this.selectedFits;
  //   fi.bucket = this.bucket;
  //   fi.statusCodes = newstat;
  //   fi.productTypes = this.selectedProductTypes;
  //   fi.categoryType = this.categoryType;
  //   fi.subCategoryType = this.subCategoryType;
  //   fi.vendorUid = this.vendorUid;
  //   fi.searchTerm = this.searchTerm;
  //   fi.compliance = this.selectedCompliance;
  //   fi.category = this.selectedCategories;
  //   fi.stories = this.selectedStories;
  //   fi.isStorySelected = this.isStorySelected;
  //   fi.offerMonths = this.selectedOfferMonths;

  //   return fi;

  // }



  // clearFilters() {
  //   this.clearCategories();
  //   this.clearCompliance();
  //   this.clearFabrics();
  //   this.clearFits();
  //   this.clearSearch();
  //   this.clearColors();
  //   this.clearStatus();
  //   this.clearProductTypes();
  //   this.clearProductStatusList();
  //   this.clearOfferMonths();
  // }

  setActiveMode(mode) {
    this.globals.activeTab = mode;
    this.router.navigate(['/h/' + mode]);
  }

  routeToCategory(cat: string) {

    this.globals.activeTab = 'collList/*';
    this.router.navigate(['/h/productList/Garment/' + cat]);
  }


  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    this.eventSubscription.unsubscribe();
  }


}
