.maincontainer{
    max-width: 85%;
    margin: auto;
    padding: 0 10px 16px;
    min-height: 320px;
    color: #282c3f;
}
.dropdown{
    margin:0 auto;
}
.innerItemContainer{
    background: #fff;
    font-size: 14px;
    border: 1px solid #eaeaec;
    border-radius: 4px;
    position: relative;
    padding: 12px 12px 0;
    cursor: pointer;
}
.itemheader{
    padding: 18px 12px 18px;
    border: 1px solid #d4d5d9;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.itemheader div {
    font-size: 16px;
    font-weight: 600;
    display: inline-block;
}

.selectedButton{
    background-color: #337ab7;
    color: white;
}

.order-detail-card{
    display: flex;
    background: #fff;
    font-size: 14px;
    border: 1px solid #eaeaec;
    border-radius: 4px;
    position: relative;
    padding: 12px 12px 0;
    cursor: pointer;
    margin-left: 10%;
}

.card-style-code{
    font-size: 18px;
    font-weight: 700;
}

.flex-item{
    padding:1em;
    box-sizing: border-box;
    height: 160px;
    width: 50%;
}

.back-button{
    position: fixed;
    bottom: 90;
    left: 176px;
    top: 9%;
    margin-top: 5px;
    margin-bottom: 10px;
}

.icon-div{
    font-size: 79px;
    margin-left: 23%;
}
 