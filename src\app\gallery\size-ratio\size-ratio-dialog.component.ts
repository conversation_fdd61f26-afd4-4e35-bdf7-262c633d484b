import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Category } from 'app/model/Category.model';
import { CustomerProfile } from 'app/model/customerprofile.model';
import { SizeRatio } from 'app/model/SizeRatio.model';
import { User } from 'app/model/user.model';
import { CatalogService } from 'app/service/catalog.service';
import { CategoryService } from 'app/service/category.service';
import { OrderService } from 'app/service/order.service';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-size-ratio-dialog',
  templateUrl: './size-ratio-dialog.component.html',
  styleUrls: ['./size-ratio-dialog.component.scss']
})
export class SizeRatioDialogComponent implements OnInit {

  @Input() cat:string = "BCSU"
  @Input() subcat:string = "BCSU"
  category: Category = new Category()
  sizeCodes: string[] = [];
  sizeRatios: string[] = ["A","B", "C", "D"];
  sizeRatioData: SizeRatio[] = [];

  ratioData: Map<string, any> = new Map<string, any>()
  customers:CustomerProfile[] =[];
  user:User =  new User();
  @Output() saveEvent: EventEmitter<any> = new EventEmitter();

  constructor(private router: Router, private route: ActivatedRoute, private orderService: OrderService,
              private modalRef: BsModalRef,
              private catalogService: CatalogService, private categoryService: CategoryService) { }

  ngOnInit(): void {
  	this.getCategory();

    this.customers = JSON.parse(localStorage.getItem("customers"));
    this.user = JSON.parse(localStorage.getItem("userProfile"));
  }

  getCategory(){
    this.categoryService.getByCode(this.subcat).subscribe(data => {
       this.category = data;
       this.sizeCodes = [];
       let j = this.category.sizeCode.length;
       let i=0;
       while(i<j){
       	 	let code = this.category.sizeCode.substring(i, i+3);
       	 	this.sizeCodes.push(code);
       	 	i+=3;
       }
       this.getSizeRatio();
    });
  }

  getSizeRatio(){
    this.orderService.getSizeRatioBySubcat(this.subcat).subscribe(data => {
      this.sizeRatioData = data;
       this.initData();
    });
  }

  initData(){
  	for(let i in this.sizeRatios){
  		let obj = [];
  		for(let j in this.sizeCodes){
        	obj.push({'qty':this.getQty(this.sizeRatios[i], this.sizeCodes[j])});
    	}
  		this.ratioData.set(this.sizeRatios[i], obj)
  	}
  }

  getQty(ratio: string, sizecode: string): string {
    //for(let sr in this.sizeRatioData){
    for(var i=0;i<this.sizeRatioData.length;i++){
      var sr = this.sizeRatioData[i];
      if(sr.sizeRatioCode == ratio && sr.sizeCode == sizecode){
        return sr?.qty?.toString() || '';
      }
    }
    return '';
  }

  getEntities(key) {
    return this.ratioData.get(key);
  }

  getEntities2(key) {
    return Array.from(this.ratioData.get(key).entries()).map(item => item[1]);
  }

  save(){
    var ratios:Array<SizeRatio> = [];
    var cust = this.customers[0];
    var i=0;
    for(i=0;i<this.ratioData.size;i++){
      var data:SizeRatio[] = this.ratioData.get(this.sizeRatios[i]);
      var j=0;
      for(let r in data){
      //  if(data[j].qty.toLocaleString() != ''){
          var ratio = new SizeRatio();
          ratio.sizeRatioCode = this.sizeRatios[i];
          ratio.sizeCode = this.sizeCodes[j];
          ratio.catUid = this.cat;
          ratio.subCatUid = this.subcat;
          ratio.qty = data[j].qty;
          ratio.parentCustomerUid = cust.parentCustomerUid;
          ratio.customerUid = this.user.customerUid;
          ratios.push(ratio);
       // }
        j++;
      }
    }

    this.orderService.updateSizeRatio(ratios).subscribe(data => {
      this.saveEvent.emit('saved');
    });

  }

	trackByIdx(index: number, obj: any): any {
	  return index;
	}

}
