
import { Category } from '../model/Category.model';
import { CategoryType } from '../model/CategoryType.model';
import { MasterDataItem } from '../model/MasterDataItem.model';

export class CatalogFilterData {

    cats: Category[] = [];
    subCats: Category[] = [];
    stories: MasterDataItem[] = [];
    brands: MasterDataItem[] = [];
    gender: MasterDataItem[] = [];
    categoryTypes: CategoryType[] = [];
    fabrics: MasterDataItem[] = [];
    offerMonths: MasterDataItem[] = [];
    fits: MasterDataItem[] = [];
    colors: MasterDataItem[] = [];

}