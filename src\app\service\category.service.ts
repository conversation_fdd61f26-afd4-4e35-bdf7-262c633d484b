import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Appconfig, Globals } from 'app/appconfig/appconfig.component';
import { Category } from 'app/model/Category.model';
import { CategoryType } from 'app/model/CategoryType.model';
import { MasterDataItem } from 'app/model/MasterDataItem.model';
import { User } from 'app/model/user.model';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {

  http: HttpClient;
  // basicAuth = this.globals.basicAuth;
  basicAuth = localStorage.getItem("basicAuth");
  user: User = JSON.parse(localStorage.getItem("userProfile"));

  constructor(private httpin: HttpClient, private globals: Globals) {
    this.http = httpin;
    this.globals.$basicAuth.subscribe(auth => {
      this.basicAuth = auth;
    });
  }


  public getByLevel(level: number): Observable<Category[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Category[]>(Appconfig.BASE_URL + 'api/category/' + level + '/level', {
      headers: headers
    });
  }

  public getProductCount(): Observable<Category[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let currentUser = JSON.parse(localStorage.getItem("currentUser"));
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Category[]>(Appconfig.BASE_URL + `api/category/all/${currentUser.uuid}`, {
      headers: headers
    });
  }

  public getByCode(code: String): Observable<Category> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<Category>(Appconfig.BASE_URL + 'api/category/' + code + '', {
      headers: headers
    });
  }


  public getTypes(): Observable<CategoryType[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<CategoryType[]>(Appconfig.BASE_URL + 'api/category/types', {
      headers: headers
    });
  }

  public getRegions(): Observable<MasterDataItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<MasterDataItem[]>(Appconfig.BASE_URL + 'api/category/REGION/masterdata', {
      headers: headers
    });
  }

  public getChannels(): Observable<MasterDataItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<MasterDataItem[]>(Appconfig.BASE_URL + 'api/category/CHANNEL/masterdata', {
      headers: headers
    });
  }


  public getBrands(): Observable<MasterDataItem[]> {
    // let basicAuth = localStorage.getItem('baiscAuth');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

    return this.http.get<MasterDataItem[]>(Appconfig.BASE_URL + 'api/category/BRAND/masterdata', {
      headers: headers
    });
  }

}
