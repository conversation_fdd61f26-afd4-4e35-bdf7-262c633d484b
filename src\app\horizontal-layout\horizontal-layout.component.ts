import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AfterViewInit, ChangeDetectorRef, Component, HostListener, Input, OnInit, TemplateRef } from '@angular/core';
import { Event, NavigationEnd, Router } from '@angular/router';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Status } from 'app/model/status.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { OrderService } from 'app/service/order.service';
import { environment } from 'environments/environment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import * as screenfull from 'screenfull';
import * as pako from 'pako';
import { Globals } from '../appconfig/appconfig.component';
import { PageTitleService } from '../core/page-title/page-title.service';
import { Category } from '../model/Category.model';
import { User } from '../model/user.model';
import { AuthService } from '../service/auth/auth.service';
import { CategoryService } from '../service/category.service';
import { FilterService } from '../service/filter.service';
import { HttpsStatusLoaderService } from '../service/http-status-loader.service';
import { CatalogService } from 'app/service/catalog.service';
import { CatalogFilter } from 'app/model/CatalogFilter.model';

declare var $: any;
interface IHeaderImageList {
  code: string;
  name: string;
  active?: boolean;
}

@Component({
  selector: 'ms-horizontal',
  templateUrl: './horizontal-layout-component.html',
  styleUrls: ['./styles.scss']
})

export class HorizontalLayoutComponent implements OnInit, AfterViewInit {

  currentLang = 'en';
  productItems: any[];
  supportNum: number;
  url: string;
  header: string;
  modalRefData: BsModalRef;
  root: any = 'ltr';
  layout: any = 'ltr';
  public innerWidth: any;
  headerSkinColor: any = "light";

  dark: boolean;
  boxed: boolean;
  customizerIn: boolean = false;
  isFullscreen: boolean = false;
  chatWindowOpen: boolean = false;
  chatSidebar: boolean = false;
  isMobile: boolean = false;
  sidebarClosed: boolean = false;


  level1: Category[] = [];
  level2: IHeaderImageList[] = [];
  level3: Category[] = [];

  cartCount: number = 0;
  wishlistCount: number = 0;

  currentUser: User = new User();
  tenantConfig: TenantConfig;

  activeTab: string = '';

  basicAuth = localStorage.getItem("basicAuth")
  tenantConfigData = JSON.parse(localStorage.getItem("tenantConfig"));
  currentUserData = JSON.parse(localStorage.getItem('currentUser'));
  user: User = JSON.parse(localStorage.getItem("userProfile"));
  files: NgxFileDropEntry[] = [];
  tenantLogo: string = '';
  status: Status = new Status()
  modalRef: NgbModalRef;
  showLoadingIndicator: boolean = false;
  tenantHomeConfigName: any;
  userLocation: any;

  private _mediaSubscription: Subscription;
  private _routerEventsSubscription: Subscription;
  private _router: Subscription;


  constructor(private pageTitleService: PageTitleService,
    public translate: TranslateService,
    private catalogService: CatalogService,
    private router: Router,
    private authService: AuthService,
    private categoryService: CategoryService,
    public _FilterService: FilterService,
    private globals: Globals,
    private orderService: OrderService,
    private toastrService: ToastrService,
    private http: HttpClient,
    private modalService: BsModalService,
    private newmodalService: NgbModal,
    private _httpLoaderService: HttpsStatusLoaderService,
    private cdRef: ChangeDetectorRef) {

    const browserLang: string = translate.getBrowserLang();
    translate.use(browserLang.match(/en|fr/) ? browserLang : 'en');
  }

  @Input() childMessage: string;

  ngAfterViewInit() {
    this.tenantHomeConfigName = this.tenantConfigData.name
    
    this.userLocation = this.currentUserData.locationCode
    /** Add oveflow-menu class for horizontal layout on small screen resolution **/
    this.innerWidth = window.innerWidth;
    // if(this.innerWidth <= 959){
    //    this.router.navigate(['/dashboard/dashboard-v1']);
    // }
    // if ($('.horizontal-menu').length > 0) {
    //    $(".horizontal-menu > li").each(function() {
    //       var $obj = $(this).offset();
    //       if (($(window).width()) - ($obj.left) < 600) {
    //          $(this).addClass("overflow-left-menu");
    //       }
    //    });
    // }
    this._httpLoaderService.getHttpStatus().subscribe((status: boolean) => {
      this.showLoadingIndicator = status;
      this.cdRef.detectChanges();
    });
  }

  ngOnInit() {

    this.orderService.$cartCount.subscribe(count => {
      this.cartCount = count;
    });

    this.orderService.$wishListCount.subscribe(count => {
      this.wishlistCount = count;
    });

    this.getAndSetCartAndWishListCount();

    this._router = this.router.events.pipe(
      filter((event: Event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
        this.url = event.url;
      });

    if (this.url != '/session/login' && this.url != '/session/register' && this.url != '/session/forgot-password' && this.url != '/session/lockscreen') {

      /** Perfect scrollbar for chat window **/
      // const elemChatbar = <HTMLElement>document.querySelector('.chat-inner ');
      // if (window.matchMedia(`(min-width: 960px)`).matches) {
      //    const ps = new PerfectScrollbar(elemChatbar);
      // }
    }

    this.pageTitleService.title.subscribe((val: string) => {
      this.header = val;
    });

    //Add class on focus of search box in header
    // document.getElementById('search-field').addEventListener("focusin", function () {
    //    document.getElementById('search-field').parentElement.classList.add("search-active");
    // })
    // document.getElementById('search-field').addEventListener("focusout", function () {
    //    document.getElementById('search-field').parentElement.classList.remove("search-active");
    // })

    this.currentUser = JSON.parse(localStorage.getItem("currentUser"));
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    this.tenantLogo = 'assets/img/' + this.tenantConfig?.tenantUid + '_logo.jpg';

    this.getLevel1Categories();
    this.getLevel2Categories();
    this.getLevel3Categories();
    // this.getItemsOfZip();


  }

  getAndSetCartAndWishListCount() {
    this.orderService.getCartSummary().subscribe();
    this.orderService.getWishListItems().subscribe();
  }

  calcTotal() {

  }

  ngOnDestroy() {
    this._router.unsubscribe();
    /**  this._mediaSubscription.unsubscribe(); **/
  }

  /** Detect the screen resolution size on resize and perform action accordingly **/
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;

    // if ($('.horizontal-menu').length > 0) {
    //    $(".horizontal-menu > li").each(function() {
    //       var $obj = $(this).offset();
    //       if (($(window).width()) - ($obj.left) < 600) {
    //          $(this).addClass("overflow-left-menu");
    //       }
    //    });
    // }

    // if(this.innerWidth <= 959){
    //    this.router.navigate(['/dashboard/dashboard-v1']);
    // }
  }

  /** Functionality of full screen event **/
  toggleFullscreen() {
    if (screenfull.isEnabled) {
      screenfull.toggle();
      this.isFullscreen = !this.isFullscreen;
    }
  }

  /**
    * customizerFunction is used to open and close the customizer.
    */
  customizerFunction() {
    this.customizerIn = !this.customizerIn;
  }

  /**
    * chatWindowFunction is used to open and close the chat window.
    */
  chatWindowFunction() {
    this.chatWindowOpen = !this.chatWindowOpen;
  }

  /**
    * chatSidebarFunction is used to open and close the chat sidebar list.
    */
  chatSidebarFunction() {
    this.chatSidebar = !this.chatSidebar;
  }

  /**
    * changeHeaderColor function filter the color for header section.
    */
  changeHeaderColor(color) {
    this.headerSkinColor = color;
  }

  /**
    * changeRTL method is used to change the layout of template.
    */
  changeRTL(isChecked) {
    if (isChecked) {
      this.layout = "rtl"
    } else {
      this.layout = "ltr"
    }
  }

  /**
    * logOut method is used to log out the  template.
    */
  logOut() {
    this.authService.logOut();
  }

  setListMode(cat: Category) {
    this._FilterService.sendClickEvent(true);
    if (cat.level == 2) {
      this.router.navigate(['/h/prodHome/' + cat.code]);
    }
    else {
      this.router.navigate(['/h/gallerylist/' + cat.parent + '/' + cat.code]);
    }

  }

  openCart() {
    this.router.navigate(['/h/cart']);
  }

  openWishlist() {
    this.router.navigate(['/h/wishlistGrid']);
  }

  payment() {
    this.router.navigate(['/h/payment']);
  }

  setActiveMode(mode: string) {
    this.globals.activeTab = mode;
    this.activeTab = mode;
    this.router.navigate(['/h/' + mode]); //, { queryParams: { 'refresh': new Date().getTime() }}
  }


  getLevel1Categories() {
    this.categoryService.getByLevel(1).subscribe(data => {
      this.level1 = data;
      this.level1.forEach(item => {
        item['active'] = false;
      });
    });
  }

  public getLevel2Categories(): void {
    this.level2 = [];
    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    var items = JSON.parse(this.tenantConfig.headerImages);
    this.categoryService.getProductCount().subscribe(data => {
      this.productData = data;
    });
    items.forEach(item => {
      var headerImageListObj = { code: item.catUid, name: item.name }
      this.level2.push(headerImageListObj)
    })
    this.level2.forEach(item => {
      item['active'] = false;
    });
  }
  productData:any;
  getLevel3Categories() {
    this.categoryService.getByLevel(3).subscribe(data => {
      this.level3 = data;
      this.level3.forEach(item => {
        item['active'] = false;
      });
    });
  }
  
  // private getProductCounts(): void {
    
  // }

  //This function is to unzip the zip api data and store data in localstorage
  //  public getItemsOfZip():void{
  //     this.productItems=[];
  //     this.catalogService.getItemsOfZip().subscribe(data => {
  //       let arrayBuffer;
  //       var reader = new FileReader();
  //       reader.readAsArrayBuffer(data);
  //       reader.onloadend = (event) => {
  //           arrayBuffer =event.target.result;
  //           const uncompressedData = JSON.parse(pako.ungzip(arrayBuffer, { to: 'string' }));
  //           localStorage.setItem('itemData', JSON.stringify(uncompressedData));
  //       };
  //     });
  //       }

  getProductCounts(items: Category[], parent: string): Category[] {
    var childs: Category[] = [];
    for (var i of this.productData) {
      if (i.parent == parent) {
        childs.push(i);
      }
    }
    return childs;  
  }

  

  getActiveMode() {
    return this.globals.activeTab;
  }

  get isDistributor() {
    return this.currentUser && this.currentUser.roles.indexOf('DISTRIBUTOR') != -1;
  }

  get isAdmin() {
    return this.currentUser && this.currentUser.roles.indexOf('ADMIN') != -1;
  }

  get isBrandmgr() {
    return this.currentUser && this.currentUser.roles.indexOf('BRANDMGR') != -1;
  }

  toggle(level: string, code: any) {
    if (level == "level1") {
      this.level1.forEach(menu => {
        if (menu.code == code) {
          menu.active = !menu.active
        }
      });
    }
    if (level == "level2") {
      this.level2.forEach(menu => {
        if (menu.code == code) {
          menu.active = !menu.active
        }
      });
    }
    if (level == "level3") {
      this.level3.forEach(menu => {
        if (menu.code == code) {
          menu.active = !menu.active
        }
      });
    }

  }

  import(content) {
    this.files = []
    this.modalRef = this.newmodalService.open(content, { backdrop: 'static', keyboard: false, centered: true, size: 'md' });
  }

  deleteFile(i) {
    this.files.splice(i, 1)
  }

  public fileOver(event) {
    console.log(event);
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }

  public fileLeave(event) {
    console.log(event);
  }

  importOrder() {
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          console.log(droppedFile.relativePath, file);
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set("ptuid", this.user.parentTenantUid);

          this.http.post(`${environment.apiUrl}/` + 'api/order/import', formData, { headers: headers, responseType: 'blob' })
            .subscribe(data => {
              this.files = [];
              this.modalRef.close();
              this.toastrService.success("Inventory data updated successfully.");

            }, error => {
              this.modalRef.close();
              this.toastrService.success(error.message);
            })
        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        console.log(droppedFile.relativePath, fileEntry);
      }
    }
  }

  public isRetailer(): boolean {
    let roles = [];
    roles = this.user.roles.split(',')
    let index = roles.findIndex(x => x.toLowerCase().trim() == 'RETAILER'.toLowerCase().trim())
    if (index > -1) {
      return true;
    } else {
      return false;
    }
  }

  // To show support number when we click on Need help?
  public needHelp(template: TemplateRef<any>): void {
    this.modalRefData = this.modalService.show(template);
    this.showNum();
  }

  public showNum(): void {

    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    this.supportNum = this.tenantConfig.supportNo;
  }
}



