import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BehaviorSubject } from 'rxjs';
import * as pako from 'pako';
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { ActivatedRoute, Router } from '@angular/router';
import { Catalog } from 'app/model/catalog.model';
import { CatalogFilter } from 'app/model/CatalogFilter.model';
import { TenantConfig } from 'app/model/TenantConfig.model';
import { CatalogService } from 'app/service/catalog.service';
import { SizeRatioDialogComponent } from './size-ratio/size-ratio-dialog.component';

import { NgbCarousel, NgbSlideEvent, NgbSlideEventSource } from '@ng-bootstrap/ng-bootstrap';
import { CustomerProfile } from 'app/model/customerprofile.model';
import { Order } from 'app/model/order.model';
import { OrderDetails } from 'app/model/OrderDetails.model';
import { OrderItem } from 'app/model/orderitem.model';
import { User } from 'app/model/user.model';
import { FilterService } from 'app/service/filter.service';
import { OrderService } from 'app/service/order.service';
import { ToastrService } from 'ngx-toastr';
import { HorizontalLayoutComponent } from 'app/horizontal-layout/horizontal-layout.component';
import { CatalogFilterComponent } from 'app/catalog-filter/catalog-filter.component';


@Component({
  selector: 'app-gallery',
  templateUrl: './gallery.component.html',
  styleUrls: ['./gallery.component.scss']
})
export class GalleryListManagementComponent implements OnInit, AfterViewInit {
  @ViewChild(CatalogFilterComponent) catalogFilterData!: CatalogFilterComponent;
  ngxInfiniteScrollConfig = {
    throttle: 300,
    scrollDistance: 1,
    scrollUpDistance: 2,
  }
  showZipResponse: boolean = false;
  galleryData: any;
  productItems: any[];
  productItem: any[];
  subcat: string;
  cat: string;
  catalogFilter: CatalogFilter;
  tenantConfig: TenantConfig;
  smallerGrid = true;
  isShowAmt: boolean = true;
  searchStyleCodeText: any = "";

  navigationSubscription;


  modalRef: BsModalRef;
  hideStickyFilter: boolean = true;
  sendProductDetails: BehaviorSubject<any> = new BehaviorSubject(null);
  isPageIntilized: boolean = false;

  constructor(private router: Router, private dialog: MatDialog, private route: ActivatedRoute,
    private toastrService: ToastrService, private orderService: OrderService,
    private catalogService: CatalogService, private modalService: BsModalService,
    private _FilterService: FilterService,
    private horizontalLayoutComponent: HorizontalLayoutComponent
  ) {
    this.isPageIntilized = false;
    // this.navigationSubscription = this.router.events.subscribe((e: any) => {
    //   if (e instanceof NavigationEnd) {
    //     this.onReload();
    //   }
    // });

  }

  ngOnInit(): void {
    // this.getItemsOfZip();
    this.route.params.subscribe(params => {
      this.cat = this.route.snapshot.params.cat;
      this.subcat = this.route.snapshot.params.subcat;
      this.onReload();
    })
    this.user = JSON.parse(localStorage.getItem("userProfile"));
    this.customers = JSON.parse(localStorage.getItem("customers"));
    let backUpFilter: any = this._FilterService.getBackUpFilterData();
    if (backUpFilter) {
      this.filterChanged(backUpFilter);
    }
  }
  ngAfterViewInit(): void {
    this.isPageIntilized = true;
  }

  onReload() {
    this.cat = this.route.snapshot.params.cat;
    this.subcat = this.route.snapshot.params.subcat;
    this.catalogFilter = new CatalogFilter();
    this.catalogFilter.subCatUids = [];
    this.catalogFilter.subCatUids.push(this.subcat);

    this.tenantConfig = JSON.parse(localStorage.getItem("tenantConfig"));
    var fetchFromApi = (this.tenantConfig?.featureSet)
    var fetchFromApiObject = JSON.parse(fetchFromApi);
    if (fetchFromApiObject.fetchFromAPI == 1) {
      this.getItems();
    }
    else if (fetchFromApiObject.fetchFromAPI == 0 || fetchFromApiObject.fetchFromAPI == null) {
      // this.getDataItems();
      this.getItemsOfZip();

    }

    if (this.tenantConfig.disableAmt != null && this.tenantConfig.disableAmt != undefined && this.tenantConfig.disableAmt == 1) {
      this.isShowAmt = false
    }



  }

  //This function is to disable the product in gallery
  public disableProduct(product): boolean {
    if (product.stockCheck && product.warehouseStockQty && product.stockCheck == 1 && product.warehouseStockQty < 1) {
      return false;
    }
    else {
      return true;
    }
  }
  public getItemsOfZip(): void {
    this.productItems = [];
    this.showZipResponse = true;
    this.catalogService.getItemsOfZip().subscribe(data => {
      let arrayBuffer;
      var reader = new FileReader();
      reader.readAsArrayBuffer(data);
      reader.onloadend = (event) => {
        arrayBuffer = event.target.result;
        const uncompressedData = JSON.parse(pako.ungzip(arrayBuffer, { to: 'string' }));
        this.galleryData = JSON.stringify(uncompressedData);
        this.productItem = JSON.parse(this.galleryData);
        this.showZipResponse = false;
        this.productItems = this.productItem?.filter((item) => item.subCatUid == this.subcat)
        this.orderService.getWishListItems().subscribe(res => {
          this.wishlist = res;
          if (this.wishlist.length > 0) {
            this.wishlist.forEach(item => {
              let index = this.productItems.map(e => e.uuid).indexOf(item.uuid);
              if (index > 0) {
                this.productItems[index].inWishlist = true;
              }
            })
          }

          this.orderService.getCartSummary().subscribe(res => {
            this.cartList = res;
            if (this.cartList.length > 0) {
              this.cartList.forEach(item => {
                let index = this.productItems.map(e => e.uuid).indexOf(item.catalogUid);
                if (index > 0) {
                  this.productItems[index].inCart = true;
                }
              })
            }
          });
        });

        // localStorage.setItem('itemData', JSON.stringify(uncompressedData));
      };
    });
  }

  //Fetch this function Data when fetchFromAPI(tenant home config table has featuresetColumn and this column has object fetchFromAPI is one of the property in featureset) property has vale 0 
  // public getDataItems(): void {
  //   this.getItemsOfZip();
  //   const itemData = this.galleryData;
  //   console.log("itemData", itemData)

  //   this.productItem = JSON.parse(itemData);

  // }


  toggleFilter() {
    this.hideStickyFilter = !this.hideStickyFilter;
    if (!this.hideStickyFilter && this.catalogFilterData) {
      this.catalogFilterData.clearFilters();
    }
  }

  closeFilter(event) {
    this.hideStickyFilter = event;
  }

  toggleGrid() {
    this.smallerGrid = !this.smallerGrid;
  }

  openSizeratioModal(template: TemplateRef<any>) {
    let config = {
      backdrop: 'static',
      ignoreBackdropClick: true,
      keyboard: false
    }
    this.modalRef = this.modalService.show(
      template,
      Object.assign({ config }, { class: 'image-popup-wrapper modal-xl' })
    );
  }
  currentProduct: any;
  getProductDetails(uid: string) {
    this.catalogService.getByUid(uid).subscribe(data => {
      this.currentProduct = data;
      this.sendProductDetails.next(data);
    });
  }

  selectedProduct: any;
  openAddToCartModal(template: TemplateRef<any>, product) {
    this.selectedProduct = product;
    let config = {
      backdrop: 'static',
      ignoreBackdropClick: true,
      keyboard: false
    }

    this.catalogService.getByUid(product.uuid).subscribe(data => {
      this.currentProduct = data;
      this.sendProductDetails.next(data);
      this.modalRef = this.modalService.show(
        template,
        Object.assign({ config }, { class: 'image-popup-wrapper modal-lg' })
      );

    });

  }

  stockMessage: string = '';
  status:string = '';
  public getSizeQuantity(data: { items: any[], stockchecksum: any[], message: string,status:string }): void {
    this.stockMessage = data.message;
    this.orderItems = data.items;
    this.stockCheckSum = data.stockchecksum;
    this.status = data.status;
    // let sum = 0;
    // this.orderItems.forEach(item => {
    //   sum = sum + item.qty;
    // });
    // if (sum > parseInt(this.product.stockCheck)) {
    //   this.toastrService.warning('quantity exceeds stock! Please enter correct quantity');
    // }
  }
  wishlist: any[] = [];
  cartList: any[] = [];
  getItems() {
    this.catalogService.getItems(this.catalogFilter).subscribe(data => {
      this.productItems = data;
      this.orderService.getWishListItems().subscribe(res => {
        this.wishlist = res;
        if (this.wishlist.length > 0) {
          this.wishlist.forEach(item => {
            let index = this.productItems.map(e => e.uuid).indexOf(item.uuid);
            if (index > 0) {
              this.productItems[index].inWishlist = true;
            }
          })
        }

        this.orderService.getCartSummary().subscribe(res => {
          this.cartList = res;
          if (this.cartList.length > 0) {
            this.cartList.forEach(item => {
              let index = this.productItems.map(e => e.uuid).indexOf(item.catalogUid);
              if (index > 0) {
                this.productItems[index].inCart = true;
              }
            })
          }
        });
      });

    });
  }

  removeFromCart(product): void {
    console.log("product", product);
  }

  getWIshlistItems(): void {
    this.orderService.getWishListItems().subscribe(res => {
      this.wishlist = res;
    });
  }

  getCartItems(): void {
    this.orderService.getCartSummary().subscribe(res => {
      this.cartList = res;
    });
  }

  //This function is call when we do filter ex:gender,color wise
  public getFilterItems(): void {
    this.productItems = [];
    this.catalogService.getItems(this.catalogFilter).subscribe(data => {
      this.productItems = data;
    });
  }



  getImgUrl(url: string) {
    if (this.tenantConfig.imageBaseUrl == undefined || this.tenantConfig.imageBaseUrl == null) {
      return url
    }
    else if (this.tenantConfig.imageBaseUrl.slice(-1) == '/') {

      return this.tenantConfig.imageBaseUrl + url;
    } else {

      return this.tenantConfig.imageBaseUrl + '/' + url;

    }


  }


  getThumbnailUrls(item: Catalog): string[] {
    if (item.thumbnailImageUid != null && item.thumbnailImageUid.length > 0) {
      return item.thumbnailImageUid.split(",")
    }
    if (item && item.imageUid != null && item.imageUid.length > 0) {
      return item.imageUid.split(",")
    }
    else if (item && item.imageUid == null || item.imageUid == undefined) {
      const storeData = [item.uuid + ".jpg"];
      return storeData;
    }
    return [];
  }


  getData() {

    for (let i = 0; i <= 80; i++) {
      this.productItems.push({
        addedToCartByUserUid: "vijayendra",
        blockedByUserUid: "vijayendra",
        likeCount: 1,
        status: 3,
        thumbnailImages: [
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470557-1.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470557-2.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470557-3.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470557-4.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470557-5.jpg'
        ],
        title: "MEN'S SKY BLUE PRINTED CHISELED FIT SHIRT",
        uuid: "d101c3a1-7d63-441f-a005-2ea49ab63680"
      })
      this.productItems.push({
        addedToCartByUserUid: "vijayendra",
        blockedByUserUid: "vijayendra",
        likeCount: 1,
        status: 3,
        thumbnailImages: [
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470855-1.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470855-2.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470855-3.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470855-4.jpg',
          'https://www.indianterrain.com/media/catalog/product/cache/ecd051e9670bd57df35c8f0b122d8aea/8/4/8470855-5.jpg'
        ],
        title: "MEN'S GREEN PRINTED SLIM FIT SHIRT",
        uuid: "d101c3a1-7d63-441f-a005-2ea49ab63680"
      })
    }
  }

  sortChanged(item: string): void {
    switch (item) {
      case 'Whats New':
        break;
      case 'Popularity':
        this.productItems.sort((a, b) => {
          return a.viewCount - b.viewCount;
        });
        break;
      case 'Price Low to High':
        this.productItems.sort((a, b) => {
          return a.price - b.price;
        });
        break;
      case 'Price High to Low':
        this.productItems.sort((a, b) => {
          return b.price - a.price;
        });
        break;
    }
  }

  public filterChanged(event) {
    let filterData: any = event;
    if (filterData == 'empty') {
      this.onReload();
    } else {
      if (Object.keys(filterData).length > 0) {
        for (let key of Object.keys(filterData)) {
          let value = filterData[key];
          if (value.toString().length > 0) {
            if (key == 'toPrice' || key == 'fromPrice') {
              this.catalogFilter[key] = value;
            } else {
              this.catalogFilter[key] = value.toString();
            }
          }
        }
        setTimeout(() => {
          this.getFilterItems();
        }, 500);
      }


    }

  }

  private applyFiltersAndGetItems(): void {

  }

  openSizeRatioDialog() {

    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;

    this.dialog.open(SizeRatioDialogComponent, dialogConfig);
  }

  @ViewChild('carousel', { static: true }) carousel: NgbCarousel;

  paused = false;
  unpauseOnArrow = false;
  pauseOnIndicator = false;
  pauseOnHover = true;

  togglePaused() {
    if (this.paused) {
      this.carousel.cycle();
    } else {
      this.carousel.pause();
    }
    this.paused = !this.paused;
  }

  onSlide(slideEvent: NgbSlideEvent) {
    if (this.unpauseOnArrow && slideEvent.paused &&
      (slideEvent.source === NgbSlideEventSource.ARROW_LEFT || slideEvent.source === NgbSlideEventSource.ARROW_RIGHT)) {
      this.togglePaused();
    }
    if (this.pauseOnIndicator && !slideEvent.paused && slideEvent.source === NgbSlideEventSource.INDICATOR) {
      this.togglePaused();
    }
  }

  mouseEnterOnCarousel(element: NgbCarousel) {
    element.interval = 1000;
    element.cycle();
    // elemen
  }

  mouseLeaveOnCarousel(element: NgbCarousel) {
    element.select('1');
    element.pause()
  }

  viewDetails(product: Catalog) {
    this.router.navigate(['/h/prodDet/' + product.uuid]);
    sessionStorage.setItem('filter', JSON.stringify(this.catalogFilter));
  }



  formatCount(count) {
    if (count == 0 || count == undefined || count == null) {
      return '';
    }
    return count;
  }

  ratioSaveEvent() {
    this.modalRef.hide();
  }


  // slides = [
  //   { img: "http://placehold.it/350x150/000000" },
  //   { img: "http://placehold.it/350x150/111111" },
  //   { img: "http://placehold.it/350x150/333333" },
  //   { img: "http://placehold.it/350x150/666666" }
  // ];
  // slideConfig = { "slidesToShow": 4, "slidesToScroll": 4 };

  // addSlide() {
  //   this.slides.push({ img: "http://placehold.it/350x150/777777" })
  // }

  // removeSlide() {
  //   this.slides.length = this.slides.length - 1;
  // }

  // slickInit(e) {
  //   console.log('slick initialized');
  // }

  // breakpoint(e) {
  //   console.log('breakpoint');
  // }

  // afterChange(e) {
  //   console.log('afterChange');
  // }

  // beforeChange(e) {
  //   console.log('beforeChange');
  // }

  customers: CustomerProfile[] = [];
  user: User;
  orderItems: OrderItem[] = []
  stockCheckSum = [];

  addToCart() {
    var det = new OrderDetails();
    var ord = new Order();
    var cust = this.customers[0]
    ord.customerUid = this.user.customerUid;
    ord.parentCustomerUid = cust.parentCustomerUid;
    det.order = ord;
    det.items = this.orderItems;
    let exceedCheck = 0;
    // console.log("this.orderItems",this.orderItems)
    // console.log("this.stockCheckSum",this.stockCheckSum)

    // if (this.stockMessage == '') {
    //   this.toastrService.error('Please enter valid quantity');
    //   return;
    // }

    //   let isStockAvailbleQty = this.orderItems.every(orderItem => {
    //     const stockItem = this.stockCheckSum.find(stockItem => stockItem.sizecode.trim() === orderItem.sizeCode.trim());
    //     return stockItem ? orderItem.qty <= stockItem.qty : false;
    // });
    console.log("this.this.status", this.status)
    if (this.orderItems.length == 0 && this.status.length == 0) {
      this.toastrService.error('Please enter quantity or rating');
      return;
    }
    console.log("this.orderItems",this.orderItems)
    this.orderItems.forEach(item => {
      let result = this.stockCheckSum.find(obj => {
        return obj.sizecode === item.sizeCode
      })
      if (result != undefined && item.qty > result.qty) {
        exceedCheck = exceedCheck + 1;
      }
    });
    if (exceedCheck > 0) {
      this.toastrService.error('quantity exceeds stock! Please make sure quantity does not exceed stock');
      return;
    } 
    this.modalRef.hide();
    
    // else {
      // if(isStockAvailbleQty){
      // console.log("det", det)

      this.orderService.saveOrUpdateOrder(det).subscribe(data => {
        this.toastrService.success('', 'Added to cart');

        let index = this.productItems.map(e => e.uuid).indexOf(this.selectedProduct.uuid);
        this.productItems[index].inCart = true;

        this.getCartItems();
      });
    // }
    // }

  }

  public addToWatchlist(product): void {
    this.catalogService.getByUid(product.uuid).subscribe(data => {
      let product = data;
      let wishList = {};
      wishList['id'] = product.id;
      wishList['name'] = product.name
      wishList['uuid'] = product.uuid
      wishList['catalogUid'] = product.uuid
      wishList['description'] = product.description
      wishList['shortCode'] = product.shortCode

      wishList['userUid'] = this.user.customerUid;
      wishList['offerMonth'] = product.offerMonth;
      wishList['offerMonthUid'] = product.offerMonthUid;
      wishList['catUid'] = product.catUid;
      wishList['subCatUid'] = product.subCatUid;

      wishList['addedOn'] = +new Date();
      wishList['price'] = product.price;
      wishList['netPrice'] = product.netPrice;
      wishList['title'] = product.title;
      wishList['status'] = product.status;
      wishList['createdDate'] = product.createdOn;
      wishList['tenantUid'] = product.tenantUid;
      wishList['opMode'] = null;
      let addWatchlist: any[] = [];
      addWatchlist.push(wishList);
      this.orderService.saveWishListItem(addWatchlist).subscribe(res => {
        this.toastrService.success('', 'Added to wishlist');
        let index = this.productItems.map(e => e.uuid).indexOf(product.uuid);
        this.productItems[index].inWishlist = true;
        this.getWIshlistItems();
      });
    });

  }

  removeFromWishlist(product): void {
    let wishList: any[] = [];
    let data = this.wishlist.filter(item => {
      return product.uuid == item.uuid
    })[0];
    // let data = product;
    data['opMode'] = 2;
    wishList.push(data);
    this.orderService.saveWishListItem(wishList).subscribe(res => {
      this.toastrService.success('', 'Removed from wishlist');
      let index = this.productItems.map(e => e.uuid).indexOf(data.uuid);
      this.productItems[index].inWishlist = false;
      this.getWIshlistItems();
    });
  }

  public prepareTitle(product): string {
    let title: string = '';
    if (product.uuid) {
      title = title + product.uuid;
    }
    if (product.title) {
      title = title + product.title;
    }
    return title;
  }
  public close():void{
    this.modalRef.hide();
    this.onReload();
  }

}
